import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

class PlaceData {
  final String placeId;
  final String name;
  final String address;
  final LatLng? location;

  PlaceData({
    required this.placeId,
    required this.name,
    required this.address,
    this.location,
  });
}

class PlacesService {
  static const String _apiKey = 'AIzaSyCW81h6F10_4BmFbjlvHdLgxv8xX3hrspw';

  /// Get address for a location using reverse geocoding
  static Future<String?> getAddressFromLatLng(LatLng latLng) async {
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=${latLng.latitude},${latLng.longitude}&key=$_apiKey'
      );

      final response = await http.get(url);
      final data = jsonDecode(response.body);

      if (data['status'] == 'OK') {
        return data['results'][0]['formatted_address'];
      }
      return null;
    } catch (e) {
      // Error handled by returning null
      return null;
    }
  }

  /// Get place predictions for autocomplete
  static Future<List<Map<String, String>>> getPlacePredictions(String input) async {
    if (input.isEmpty) {
      return [];
    }

    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$input&key=$_apiKey&components=country:us'
      );

      final response = await http.get(url);
      final data = jsonDecode(response.body);

      if (data['status'] == 'OK') {
        final predictions = data['predictions'] as List;
        List<Map<String, String>> results = [];

        for (var prediction in predictions) {
          results.add({
            'placeId': prediction['place_id'] as String,
            'description': prediction['description'] as String,
          });
        }

        return results;
      }

      // API returned non-OK status
      return [];
    } catch (e) {
      // Error handled by returning empty list
      return [];
    }
  }

  /// Get place details from a place ID
  static Future<PlaceData?> getPlaceDetails(String placeId) async {
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=$_apiKey&fields=name,formatted_address,geometry'
      );

      final response = await http.get(url);
      final data = jsonDecode(response.body);

      if (data['status'] == 'OK') {
        final result = data['result'];
        final location = result['geometry']['location'];

        return PlaceData(
          placeId: placeId,
          name: result['name'],
          address: result['formatted_address'],
          location: LatLng(location['lat'], location['lng']),
        );
      }

      // API returned non-OK status
      return null;
    } catch (e) {
      // Error handled by returning null
      return null;
    }
  }

  /// Create a marker for a place
  static Marker createMarker({
    required String id,
    required LatLng position,
    required String title,
    String? snippet,
    BitmapDescriptor icon = BitmapDescriptor.defaultMarker,
    VoidCallback? onTap,
  }) {
    return Marker(
      markerId: MarkerId(id),
      position: position,
      infoWindow: InfoWindow(
        title: title,
        snippet: snippet,
      ),
      icon: icon,
      onTap: onTap,
    );
  }
}
