import 'package:flutter/material.dart';

enum EngineType {
  petrol,
  diesel,
  electric,
  hybrid
}

extension EngineTypeExtension on EngineType {
  String get displayName {
    switch (this) {
      case EngineType.petrol:
        return 'Petrol';
      case EngineType.diesel:
        return 'Diesel';
      case EngineType.electric:
        return 'Electric';
      case EngineType.hybrid:
        return 'Hybrid';
    }
  }

  Color get color {
    switch (this) {
      case EngineType.petrol:
        return Colors.orange;
      case EngineType.diesel:
        return Colors.brown;
      case EngineType.electric:
        return Colors.blue;
      case EngineType.hybrid:
        return Colors.green;
    }
  }

  IconData get icon {
    switch (this) {
      case EngineType.petrol:
        return Icons.local_gas_station;
      case EngineType.diesel:
        return Icons.local_gas_station;
      case EngineType.electric:
        return Icons.electric_car;
      case EngineType.hybrid:
        return Icons.electric_car;
    }
  }
}

class VehicleInfo {
  EngineType engineType;
  double fuelConsumption; // L/100km or kWh/100km for electric
  double fuelPrice; // £ per litre
  double electricityPrice; // £ per kWh
  double? customEmissionsRate; // kg CO2e per km (optional, user-defined)

  // Default emissions values based on the provided table
  static const Map<EngineType, double> defaultEmissionsPerKm = {
    EngineType.diesel: 0.180, // kg CO2e per km
    EngineType.petrol: 0.170, // kg CO2e per km
    EngineType.hybrid: 0.113, // kg CO2e per km
    EngineType.electric: 0.026, // kg CO2e per km
  };

  static const Map<EngineType, double> defaultEmissionsPerLitre = {
    EngineType.diesel: 2.66, // kg CO2e per litre
    EngineType.petrol: 2.35, // kg CO2e per litre
    EngineType.hybrid: 1.80, // kg CO2e per litre (equivalent)
    EngineType.electric: 0.0, // kg CO2e per litre (tailpipe)
  };

  // Default electricity price in £ per kWh
  static const double defaultElectricityPrice = 0.27;

  VehicleInfo({
    this.engineType = EngineType.petrol,
    this.fuelConsumption = 8.0, // Default value
    this.fuelPrice = 1.50, // Default value in £
    this.electricityPrice = defaultElectricityPrice, // Default value in £ per kWh
    this.customEmissionsRate, // Optional custom emissions rate
  });

  // Calculate emissions for a given distance in km with optional gradient and traffic factors
  double calculateEmissions(double distanceKm, [double gradientFactor = 1.0, double trafficFactor = 1.0]) {
    // If user has provided a custom emissions rate, use that directly
    if (customEmissionsRate != null) {
      // Apply gradient factor with reduced effect for electric vehicles
      if (engineType == EngineType.electric) {
        double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
        double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
        return distanceKm * customEmissionsRate! * electricGradientFactor * electricTrafficFactor;
      } else {
        return distanceKm * customEmissionsRate! * gradientFactor * trafficFactor;
      }
    }

    // Otherwise, use the default calculation based on fuel type
    switch (engineType) {
      case EngineType.petrol:
        // Two calculation methods:
        // 1. Using emissions per litre
        // return (fuelConsumption * distanceKm * gradientFactor * trafficFactor / 100) * defaultEmissionsPerLitre[engineType]!;
        // 2. Using emissions per km (more direct)
        return distanceKm * defaultEmissionsPerKm[engineType]! * gradientFactor * trafficFactor;

      case EngineType.diesel:
        return distanceKm * defaultEmissionsPerKm[engineType]! * gradientFactor * trafficFactor;

      case EngineType.electric:
        // Electric vehicles are less affected by gradient and traffic
        double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
        double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
        return distanceKm * defaultEmissionsPerKm[engineType]! * electricGradientFactor * electricTrafficFactor;

      case EngineType.hybrid:
        return distanceKm * defaultEmissionsPerKm[engineType]! * gradientFactor * trafficFactor;
    }
  }

  // Calculate fuel cost for a given distance in km with optional gradient and traffic factors
  double calculateFuelCost(double distanceKm, [double gradientFactor = 1.0, double trafficFactor = 1.0]) {
    switch (engineType) {
      case EngineType.petrol:
      case EngineType.diesel:
        return (fuelConsumption * distanceKm * gradientFactor * trafficFactor / 100) * fuelPrice;
      case EngineType.electric:
        // For electric vehicles, use the electricity price and consumption rate
        // Gradient affects electric vehicles less than combustion engines
        double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
        // Traffic affects electric vehicles less than combustion engines
        double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
        // Use the dedicated electricity price field with default fallback
        double pricePerKWh = electricityPrice > 0 ? electricityPrice : defaultElectricityPrice;
        // Use fuelConsumption as kWh/100km for electric vehicles
        double consumptionRate = fuelConsumption > 0 ? fuelConsumption : 20.0; // Default 20 kWh/100km
        return (distanceKm / 100) * consumptionRate * electricGradientFactor * electricTrafficFactor * pricePerKWh;
      case EngineType.hybrid:
        // For hybrid, calculate both fuel and electricity costs
        // Assume 70% of distance on fuel, 30% on electricity
        double fuelCost = (fuelConsumption * distanceKm * 0.7 * gradientFactor * trafficFactor / 100) * fuelPrice;

        // Electric portion with reduced gradient and traffic effects
        double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
        double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
        double pricePerKWh = electricityPrice > 0 ? electricityPrice : defaultElectricityPrice;
        // Assume 10 kWh/100km for the electric portion of hybrid
        double electricCost = (distanceKm * 0.3 / 100) * 10 * electricGradientFactor * electricTrafficFactor * pricePerKWh;

        return fuelCost + electricCost;
    }
  }
}
