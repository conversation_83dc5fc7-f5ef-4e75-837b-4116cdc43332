import 'package:flutter/material.dart';
import '../models/saved_route.dart';
import '../services/saved_routes_service.dart';
import '../utils/map_utils.dart';
import '../services/routing_service.dart';

class SavedRoutesScreen extends StatefulWidget {
  final Function(SavedRoute) onRouteSelected;

  const SavedRoutesScreen({
    super.key,
    required this.onRouteSelected,
  });

  @override
  SavedRoutesScreenState createState() => SavedRoutesScreenState();
}

class SavedRoutesScreenState extends State<SavedRoutesScreen> {
  List<SavedRoute> _savedRoutes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedRoutes();
  }

  Future<void> _loadSavedRoutes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get saved routes
      final routes = await SavedRoutesService.getLocalRoutes();

      setState(() {
        _savedRoutes = routes;
        _isLoading = false;
      });
    } catch (e) {
      // Error handled by updating UI state
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteRoute(SavedRoute route) async {
    try {
      final success = await SavedRoutesService.deleteLocalRoute(route.id);

      if (success) {
        setState(() {
          _savedRoutes.removeWhere((r) => r.id == route.id);
        });

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Route "${route.name}" deleted')),
        );
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete route')),
        );
      }
    } catch (e) {
      // Show error message to user
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved Routes'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSavedRoutes,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _savedRoutes.isEmpty
              ? _buildEmptyState()
              : _buildRoutesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.route,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No saved routes',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Save routes to access them quickly later',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.map),
            label: const Text('Go to Map'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoutesList() {
    return ListView.builder(
      itemCount: _savedRoutes.length,
      itemBuilder: (context, index) {
        final route = _savedRoutes[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            isThreeLine: true,
            title: Text(
              route.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      width: 45,
                      child: Text(
                        'From:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        route.startAddress,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      width: 45,
                      child: Text(
                        'To:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        route.endAddress,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 8, // gap between adjacent chips
                  runSpacing: 4, // gap between lines
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.directions_car,
                          size: 16,
                          color: route.routeType.color,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            '${route.distance} • ${route.duration}',
                            style: TextStyle(
                              color: route.routeType.color,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: route.routeType.color.withAlpha(25), // ~0.1 opacity
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: route.routeType.color.withAlpha(76)), // ~0.3 opacity
                      ),
                      child: Text(
                        route.routeType.displayName,
                        style: TextStyle(
                          color: route.routeType.color,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _deleteRoute(route),
              tooltip: 'Delete',
            ),
            onTap: () {
              Navigator.pop(context);
              widget.onRouteSelected(route);
            },
          ),
        );
      },
    );
  }
}
