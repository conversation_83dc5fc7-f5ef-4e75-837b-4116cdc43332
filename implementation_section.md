# Implementation

This section describes the key implementation details of the eco_route application, focusing on the core algorithms, data structures, and technical challenges that were addressed during development.

## Core Data Structures

### Vehicle Information Model

The application's eco-friendly routing capabilities are built upon a robust `VehicleInfo` model that encapsulates all relevant vehicle characteristics:

```dart
class VehicleInfo {
  EngineType engineType;  // petrol, diesel, hybrid, or electric
  double fuelConsumption; // L/100km or kWh/100km for electric
  double fuelPrice;       // £ per litre
  double electricityPrice; // £ per kWh
  double? customEmissionsRate; // kg CO2e per km (optional)
}
```

This model serves as the foundation for all emissions and fuel consumption calculations. The implementation uses default emissions values based on engine type, with the ability for users to override these with custom values. This approach balances ease of use with flexibility for environmentally-conscious users who may have specific data about their vehicle's emissions profile.

### Journey Statistics

To track completed journeys and provide meaningful statistics, I implemented a comprehensive `JourneyStats` model:

```dart
class JourneyStats {
  final String id;
  final DateTime completedAt;
  final String routeName;
  final String startAddress;
  final String endAddress;
  final String distance;
  final int distanceValue;
  final String duration;
  final int durationValue;
  final double emissions;
  final double fuelConsumption;
  final double fuelCost;
  final String actualDuration;
  final int actualDurationSeconds;

  // Comparison data
  final double fastestRouteEmissions;
  final double fastestRouteFuelConsumption;
  final double fastestRouteFuelCost;
  final double emissionsSaved;
  final double fuelSaved;
  final double costSaved;
}
```

This data structure not only captures basic journey information but also stores comparative data between the chosen route and the fastest route, enabling the calculation of environmental and financial savings. The model includes both formatted strings for display purposes and raw numerical values for calculations, providing flexibility in how the data can be used throughout the application.

## Algorithms and Technical Solutions

### Eco-Friendly Routing Algorithm

The core of the application is the eco-friendly routing algorithm implemented in the `RoutingService` class. This algorithm combines data from multiple sources to calculate the most environmentally friendly route:

1. **Multiple Route Options**: The implementation makes parallel API requests to Google's Routes API with different routing preferences to generate diverse route options:
   - Standard routes with traffic awareness
   - Routes that avoid highways (often more eco-friendly for certain vehicle types)

2. **Route Deduplication**: To prevent showing similar routes, I implemented a similarity detection algorithm that compares encoded polylines:

```dart
bool areRoutesSimilar(String polyline1, String polyline2) {
  // Implementation that compares key points along routes
  // to determine if they follow substantially the same path
}
```

3. **Environmental Impact Calculation**: For each route, the application calculates:
   - Emissions based on vehicle type, distance, and route characteristics
   - Fuel consumption with adjustments for route conditions
   - Financial cost based on current fuel/electricity prices

### Traffic and Gradient Factors

A significant technical challenge was incorporating real-world conditions into the emissions calculations. I implemented two key adjustment factors:

1. **Traffic Factor**: Using the Google Routes API's traffic data, the application calculates a traffic factor that adjusts fuel consumption based on congestion levels:

```dart
Map<String, dynamic> _calculateTrafficFactor(List<dynamic>? speedReadingIntervals, int totalPoints) {
  // Calculate percentages of route in different traffic conditions
  // Apply congestion factors to each segment
  // Return weighted average traffic factor and breakdown
}
```

2. **Gradient Factor**: To account for the impact of elevation changes on fuel consumption, I integrated with the Elevation API:

```dart
double calculateGradientFactor(List<LatLng> routePoints, List<double> elevations) {
  // Calculate elevation changes along route
  // Apply physics-based model to determine impact on fuel consumption
  // Return gradient factor (>1.0 for routes with significant climbs)
}
```

These factors are applied differently based on engine type, with electric vehicles being less affected by traffic but still impacted by elevation changes.

### Local Storage Implementation

After removing Firebase dependencies, I implemented a local storage solution using SharedPreferences to persist journey statistics and saved routes:

```dart
static Future<bool> saveJourneyStats(JourneyStats stats) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    final List<JourneyStats> existingStats = await getJourneyStats();
    existingStats.add(stats);
    final List<String> jsonList = existingStats.map((stat) => jsonEncode(stat.toJson())).toList();
    return await prefs.setStringList(_journeyStatsKey, jsonList);
  } catch (e) {
    return false;
  }
}
```

This approach provides a lightweight, device-local storage solution that maintains user privacy while still enabling the core functionality of route saving and journey statistics.

## Technical Challenges and Solutions

### Challenge: Route Comparison and Labeling

One significant challenge was implementing a system to accurately label routes as "eco route," "fastest route," or "fastest + eco route" when a route satisfied both criteria. The solution involved:

1. Analyzing all available routes after calculation
2. Identifying the fastest route (minimum duration)
3. Identifying the most eco-friendly route (minimum emissions)
4. Applying appropriate labels through the `RouteType` enum

This approach enables users to make informed decisions about route selection based on their priorities.

### Challenge: Vehicle-Specific Emissions Modeling

Different vehicle types have significantly different emissions profiles and respond differently to traffic and gradient conditions. I implemented a flexible emissions calculation system that:

1. Uses different base emissions rates for each engine type
2. Applies traffic and gradient factors with reduced impact for electric/hybrid vehicles
3. Supports custom emissions rates for users with specific vehicle data

```dart
double calculateEmissions(double distanceKm, double gradientFactor, double trafficFactor) {
  // If user has provided a custom emissions rate, use that directly
  if (customEmissionsRate != null) {
    // Apply gradient factor with reduced effect for electric vehicles
    if (engineType == EngineType.electric) {
      double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
      double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
      return distanceKm * customEmissionsRate! * electricGradientFactor * electricTrafficFactor;
    } else {
      return distanceKm * customEmissionsRate! * gradientFactor * trafficFactor;
    }
  }

  // Otherwise use the default calculation based on engine type
  // ...
}
```

### Challenge: Handling API Limitations

The Google Routes API has certain limitations that required creative solutions:

1. **Limited Alternative Routes**: To generate more diverse route options, I implemented multiple API calls with different routing preferences.
2. **Traffic Data Integration**: The API provides traffic data in segments, requiring a custom algorithm to calculate the overall traffic impact on fuel consumption.
3. **API Quota Management**: To manage API costs, I implemented efficient polyline encoding/decoding to minimize the number of elevation API calls needed.

## User Interface Implementation

The UI implementation focused on providing clear, actionable information about route options:

1. A collapsible bottom sheet that shows route options with clear labeling
2. A vehicle settings sidebar that allows detailed configuration of vehicle parameters
3. Journey completion screens that highlight environmental and financial savings

The implementation uses Flutter's widget system with a focus on reusable components and responsive design to ensure a consistent experience across different device sizes.
