import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../services/routing_service.dart';
import '../services/journey_stats_service.dart';
import '../models/journey_stats.dart';
import '../utils/map_utils.dart';

class JourneyScreen extends StatefulWidget {
  final RouteInfo route;
  final String startAddress;
  final String endAddress;
  final LatLng startLocation;
  final LatLng endLocation;
  final RouteInfo? fastestRoute; // The fastest route for comparison

  const JourneyScreen({
    super.key,
    required this.route,
    required this.startAddress,
    required this.endAddress,
    required this.startLocation,
    required this.endLocation,
    this.fastestRoute,
  });

  @override
  JourneyScreenState createState() => JourneyScreenState();
}

class JourneyScreenState extends State<JourneyScreen> {
  late GoogleMapController _mapController;
  Set<Polyline> _polylines = {};
  Set<Marker> _markers = {};

  // Journey tracking
  DateTime? _journeyStartTime;
  DateTime? _journeyEndTime;
  Timer? _journeyTimer;
  String _elapsedTime = "00:00:00";
  bool _isJourneyActive = false;
  bool _isJourneyComplete = false;

  // User location tracking
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _currentUserLocation;

  @override
  void initState() {
    super.initState();
    _setupRoute();
  }

  @override
  void dispose() {
    _journeyTimer?.cancel();
    _positionStreamSubscription?.cancel();
    super.dispose();
  }

  void _setupRoute() {
    // Create polyline for the route
    final routePolyline = Polyline(
      polylineId: const PolylineId('journey_route'),
      points: decodePolyline(widget.route.points),
      color: widget.route.routeType.color,
      width: 5,
    );

    // Create markers for start and end locations
    final startMarker = Marker(
      markerId: const MarkerId('start_location'),
      position: widget.startLocation,
      infoWindow: InfoWindow(title: 'Start', snippet: widget.startAddress),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
    );

    final endMarker = Marker(
      markerId: const MarkerId('end_location'),
      position: widget.endLocation,
      infoWindow: InfoWindow(title: 'Destination', snippet: widget.endAddress),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
    );

    setState(() {
      _polylines = {routePolyline};
      _markers = {startMarker, endMarker};
    });
  }

  Future<void> _startJourney() async {
    // Request location permission if not granted
    final permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      final requestedPermission = await Geolocator.requestPermission();
      if (requestedPermission == LocationPermission.denied) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location permission denied')),
        );
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location permission permanently denied. Please enable it in settings.'),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // Start tracking user location
    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    ).listen(_updateUserLocation);

    // Start journey timer
    setState(() {
      _journeyStartTime = DateTime.now();
      _isJourneyActive = true;
    });

    _journeyTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_journeyStartTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_journeyStartTime!);

        // Format as HH:MM:SS
        final hours = difference.inHours.toString().padLeft(2, '0');
        final minutes = (difference.inMinutes % 60).toString().padLeft(2, '0');
        final seconds = (difference.inSeconds % 60).toString().padLeft(2, '0');

        setState(() {
          _elapsedTime = "$hours:$minutes:$seconds";
        });
      }
    });

    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Journey started! Drive safely.')),
    );
  }

  Future<void> _completeJourney() async {
    // Stop journey timer
    _journeyTimer?.cancel();
    _positionStreamSubscription?.cancel();

    setState(() {
      _journeyEndTime = DateTime.now();
      _isJourneyActive = false;
      _isJourneyComplete = true;
    });

    // Save journey statistics
    if (_journeyStartTime != null && _journeyEndTime != null) {
      final journeyDuration = _journeyEndTime!.difference(_journeyStartTime!);
      final actualDurationSeconds = journeyDuration.inSeconds;

      // Format actual duration
      final hours = journeyDuration.inHours.toString().padLeft(2, '0');
      final minutes = (journeyDuration.inMinutes % 60).toString().padLeft(2, '0');
      final seconds = (journeyDuration.inSeconds % 60).toString().padLeft(2, '0');
      final actualDuration = "$hours:$minutes:$seconds";

      // Get fastest route data for comparison
      double fastestRouteEmissions = 0.0;
      double fastestRouteFuelConsumption = 0.0;
      double fastestRouteFuelCost = 0.0;

      // If we have the fastest route and it's different from the selected route
      if (widget.fastestRoute != null) {
        // If the selected route is already the fastest route, there are no savings
        if (widget.route.routeType == RouteType.fastest ||
            widget.route.routeType == RouteType.fastestAndEco) {
          // No savings, both routes are the same
          fastestRouteEmissions = widget.route.emissions;
          fastestRouteFuelConsumption = widget.route.fuelConsumption;
          fastestRouteFuelCost = widget.route.fuelCost;
        } else {
          // There are potential savings, use the fastest route data
          fastestRouteEmissions = widget.fastestRoute!.emissions;
          fastestRouteFuelConsumption = widget.fastestRoute!.fuelConsumption;
          fastestRouteFuelCost = widget.fastestRoute!.fuelCost;
        }
      }

      // Create journey stats
      final journeyStats = JourneyStatsService.createJourneyStats(
        routeName: widget.route.summary,
        startAddress: widget.startAddress,
        endAddress: widget.endAddress,
        distance: widget.route.distance,
        distanceValue: widget.route.distanceValue.toInt(),
        duration: widget.route.duration,
        durationValue: widget.route.durationValue.toInt(),
        emissions: widget.route.emissions,
        fuelConsumption: widget.route.fuelConsumption,
        fuelCost: widget.route.fuelCost,
        actualDuration: actualDuration,
        actualDurationSeconds: actualDurationSeconds,
        fastestRouteEmissions: fastestRouteEmissions,
        fastestRouteFuelConsumption: fastestRouteFuelConsumption,
        fastestRouteFuelCost: fastestRouteFuelCost,
      );

      // Save to storage
      await JourneyStatsService.saveJourneyStats(journeyStats);
    }

    // Show journey summary
    _showJourneySummary();
  }

  void _updateUserLocation(Position position) {
    final userLocation = LatLng(position.latitude, position.longitude);

    // Create or update user location marker
    final userMarker = Marker(
      markerId: const MarkerId('user_location'),
      position: userLocation,
      infoWindow: const InfoWindow(title: 'Your Location'),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
    );

    setState(() {
      _currentUserLocation = userLocation;
      _markers = {
        ..._markers.where((m) => m.markerId.value != 'user_location'),
        userMarker,
      };
    });

    // Follow user on map
    _mapController.animateCamera(CameraUpdate.newLatLng(userLocation));

    // Check if user is near destination (within 100 meters)
    final distanceToDestination = Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      widget.endLocation.latitude,
      widget.endLocation.longitude,
    );

    if (distanceToDestination < 100 && _isJourneyActive && !_isJourneyComplete) {
      // Suggest completing the journey
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('You have arrived at your destination!'),
          action: SnackBarAction(
            label: 'Complete Journey',
            onPressed: _completeJourney,
          ),
          duration: const Duration(seconds: 10),
        ),
      );
    }
  }

  void _showJourneySummary() {
    if (_journeyStartTime == null || _journeyEndTime == null) return;

    final journeyDuration = _journeyEndTime!.difference(_journeyStartTime!);
    final hours = journeyDuration.inHours;
    final minutes = (journeyDuration.inMinutes % 60);
    final seconds = (journeyDuration.inSeconds % 60);

    final durationText = hours > 0
        ? '$hours hr ${minutes.toString().padLeft(2, '0')} min'
        : '${minutes.toString().padLeft(2, '0')} min ${seconds.toString().padLeft(2, '0')} sec';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Journey Complete'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Distance: ${widget.route.distance}'),
            const SizedBox(height: 8),
            Text('Actual Duration: $durationText'),
            const SizedBox(height: 8),
            Text('Estimated Duration: ${widget.route.duration}'),
            const SizedBox(height: 16),
            Text(
              'Emissions: ${widget.route.emissions.toStringAsFixed(1)} kg CO₂',
              style: TextStyle(color: Colors.green.shade800),
            ),
            if (widget.route.fuelConsumption > 0)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Fuel Used: ${widget.route.fuelConsumption.toStringAsFixed(1)} L',
                  style: TextStyle(color: Colors.green.shade800),
                ),
              ),
            if (widget.route.fuelCost > 0)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Cost: £${widget.route.fuelCost.toStringAsFixed(2)}',
                  style: TextStyle(color: Colors.green.shade800),
                ),
              ),

            // Show savings compared to fastest route if available
            if (widget.fastestRoute != null &&
                (widget.route.routeType != RouteType.fastest &&
                 widget.route.routeType != RouteType.fastestAndEco))
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  const Text(
                    'Compared to Fastest Route:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Emissions Saved: ${(widget.fastestRoute!.emissions - widget.route.emissions).toStringAsFixed(1)} kg CO₂',
                    style: TextStyle(
                      color: widget.fastestRoute!.emissions > widget.route.emissions
                          ? Colors.green.shade800
                          : Colors.red.shade800,
                    ),
                  ),
                  if (widget.route.fuelConsumption > 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        'Fuel Saved: ${(widget.fastestRoute!.fuelConsumption - widget.route.fuelConsumption).toStringAsFixed(1)} L',
                        style: TextStyle(
                          color: widget.fastestRoute!.fuelConsumption > widget.route.fuelConsumption
                              ? Colors.green.shade800
                              : Colors.red.shade800,
                        ),
                      ),
                    ),
                  if (widget.route.fuelCost > 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        'Money Saved: £${(widget.fastestRoute!.fuelCost - widget.route.fuelCost).toStringAsFixed(2)}',
                        style: TextStyle(
                          color: widget.fastestRoute!.fuelCost > widget.route.fuelCost
                              ? Colors.green.shade800
                              : Colors.red.shade800,
                        ),
                      ),
                    ),
                ],
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Return to map screen
            },
            child: const Text('Return to Map'),
          ),
        ],
      ),
    );
  }

  // Build the appropriate floating action button based on journey state
  Widget _buildFloatingActionButton() {
    if (!_isJourneyActive && !_isJourneyComplete) {
      // Journey not started yet - show Start Journey button
      return FloatingActionButton.extended(
        onPressed: _startJourney,
        label: const Text('Start Journey'),
        icon: const Icon(Icons.navigation),
        backgroundColor: Colors.green,
      );
    } else if (_isJourneyActive && !_isJourneyComplete) {
      // Journey in progress - show Complete Journey button
      return FloatingActionButton.extended(
        onPressed: _completeJourney,
        label: const Text('Complete Journey'),
        icon: const Icon(Icons.done_all),
        backgroundColor: Colors.blue,
      );
    } else {
      // Journey completed - no button needed
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation'),
        actions: [
          if (_isJourneyActive && !_isJourneyComplete)
            TextButton.icon(
              icon: const Icon(Icons.cancel, color: Colors.red),
              label: const Text('Cancel', style: TextStyle(color: Colors.red)),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Cancel Journey?'),
                    content: const Text('Are you sure you want to cancel this journey? Your progress will be lost.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('No, Continue'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context); // Close dialog
                          Navigator.pop(context); // Return to map screen
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Yes, Cancel Journey'),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: Stack(
        children: [
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: widget.startLocation,
              zoom: 14.0,
            ),
            onMapCreated: (controller) {
              _mapController = controller;

              // Show the entire route
              final points = decodePolyline(widget.route.points);
              final bounds = boundsFromLatLngList(points);
              _mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
            },
            polylines: _polylines,
            markers: _markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
          ),
          // Journey info panel
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: widget.route.routeType.color.withAlpha(100),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Route type and journey status
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.route,
                              color: widget.route.routeType.color,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              widget.route.routeType.displayName,
                              style: TextStyle(
                                color: widget.route.routeType.color,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        // Journey status indicator
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _isJourneyActive
                                ? Colors.green.withAlpha(50)
                                : (_isJourneyComplete
                                    ? Colors.blue.withAlpha(50)
                                    : Colors.grey.withAlpha(50)),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _isJourneyActive
                                  ? Colors.green
                                  : (_isJourneyComplete ? Colors.blue : Colors.grey),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            _isJourneyActive
                                ? 'In Progress'
                                : (_isJourneyComplete ? 'Completed' : 'Not Started'),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: _isJourneyActive
                                  ? Colors.green
                                  : (_isJourneyComplete ? Colors.blue : Colors.grey),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Journey details
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'DISTANCE',
                                style: TextStyle(fontSize: 10, color: Colors.grey),
                              ),
                              Text(
                                widget.route.distance,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'EST. TIME',
                                style: TextStyle(fontSize: 10, color: Colors.grey),
                              ),
                              Text(
                                widget.route.duration,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (widget.route.emissions > 0)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'EMISSIONS',
                                  style: TextStyle(fontSize: 10, color: Colors.grey),
                                ),
                                Text(
                                  '${widget.route.emissions.toStringAsFixed(1)} kg',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green.shade800,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                    // Elapsed time (only shown when journey is active or complete)
                    if (_isJourneyActive || _isJourneyComplete)
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.timer,
                              size: 16,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Elapsed Time: $_elapsedTime',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Journey progress message
                    if (_isJourneyActive)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          _currentUserLocation != null
                              ? 'Navigating to destination...'
                              : 'Waiting for location...',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
