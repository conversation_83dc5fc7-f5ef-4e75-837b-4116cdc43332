import 'package:flutter_test/flutter_test.dart';
import 'package:eco_route/models/journey_stats.dart';

// Mock implementation of JourneyStats for testing
class MockJourneyStats {
  final String id;
  final DateTime completedAt;
  final String routeName;
  final String startAddress;
  final String endAddress;
  final String distance;
  final int distanceValue;
  final String duration;
  final int durationValue;
  final double emissions;
  final double fuelConsumption; // in litres
  final double fuelCost;
  final String actualDuration;
  final int actualDurationSeconds;

  // Comparison data
  final double fastestRouteEmissions;
  final double fastestRouteFuelConsumption;
  final double fastestRouteFuelCost;
  final double emissionsSaved; // emissions saved compared to fastest route
  final double fuelSaved; // litres saved compared to fastest route
  final double costSaved; // money saved compared to fastest route

  MockJourneyStats({
    required this.id,
    required this.completedAt,
    required this.routeName,
    required this.startAddress,
    required this.endAddress,
    required this.distance,
    required this.distanceValue,
    required this.duration,
    required this.durationValue,
    required this.emissions,
    required this.fuelConsumption,
    required this.fuelCost,
    required this.actualDuration,
    required this.actualDurationSeconds,
    this.fastestRouteEmissions = 0.0,
    this.fastestRouteFuelConsumption = 0.0,
    this.fastestRouteFuelCost = 0.0,
    this.emissionsSaved = 0.0,
    this.fuelSaved = 0.0,
    this.costSaved = 0.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'completedAt': completedAt.toIso8601String(),
      'routeName': routeName,
      'startAddress': startAddress,
      'endAddress': endAddress,
      'distance': distance,
      'distanceValue': distanceValue,
      'duration': duration,
      'durationValue': durationValue,
      'emissions': emissions,
      'fuelConsumption': fuelConsumption,
      'fuelCost': fuelCost,
      'actualDuration': actualDuration,
      'actualDurationSeconds': actualDurationSeconds,
      'fastestRouteEmissions': fastestRouteEmissions,
      'fastestRouteFuelConsumption': fastestRouteFuelConsumption,
      'fastestRouteFuelCost': fastestRouteFuelCost,
      'emissionsSaved': emissionsSaved,
      'fuelSaved': fuelSaved,
      'costSaved': costSaved,
    };
  }

  factory MockJourneyStats.fromJson(Map<String, dynamic> json) {
    return MockJourneyStats(
      id: json['id'],
      completedAt: DateTime.parse(json['completedAt']),
      routeName: json['routeName'],
      startAddress: json['startAddress'],
      endAddress: json['endAddress'],
      distance: json['distance'],
      distanceValue: json['distanceValue'],
      duration: json['duration'],
      durationValue: json['durationValue'],
      emissions: json['emissions'],
      fuelConsumption: json['fuelConsumption'],
      fuelCost: json['fuelCost'],
      actualDuration: json['actualDuration'],
      actualDurationSeconds: json['actualDurationSeconds'],
      fastestRouteEmissions: json['fastestRouteEmissions'] ?? 0.0,
      fastestRouteFuelConsumption: json['fastestRouteFuelConsumption'] ?? 0.0,
      fastestRouteFuelCost: json['fastestRouteFuelCost'] ?? 0.0,
      emissionsSaved: json['emissionsSaved'] ?? 0.0,
      fuelSaved: json['fuelSaved'] ?? 0.0,
      costSaved: json['costSaved'] ?? 0.0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MockJourneyStats && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Mock implementation of AggregatedJourneyStats for testing
class MockAggregatedJourneyStats {
  final int totalJourneys;
  final int totalDistanceMeters;
  final int totalDurationSeconds;
  final int totalActualDurationSeconds;
  final double totalEmissions;
  final double totalFuelConsumption;
  final double totalFuelCost;
  final double totalEmissionsSaved;
  final double totalFuelSaved;
  final double totalCostSaved;

  MockAggregatedJourneyStats({
    this.totalJourneys = 0,
    this.totalDistanceMeters = 0,
    this.totalDurationSeconds = 0,
    this.totalActualDurationSeconds = 0,
    this.totalEmissions = 0.0,
    this.totalFuelConsumption = 0.0,
    this.totalFuelCost = 0.0,
    this.totalEmissionsSaved = 0.0,
    this.totalFuelSaved = 0.0,
    this.totalCostSaved = 0.0,
  });

  String get totalDistance => formatDistance(totalDistanceMeters);
  String get totalDuration => formatDuration(totalDurationSeconds);
  String get totalActualDuration => formatDuration(totalActualDurationSeconds);

  static String formatDuration(int seconds) {
    if (seconds == 0) return '';

    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    final parts = <String>[];
    if (hours > 0) parts.add('${hours}h');
    if (minutes > 0) parts.add('${minutes}m');
    if (remainingSeconds > 0) parts.add('${remainingSeconds}s');

    return parts.join(' ');
  }

  static String formatDistance(int meters) {
    if (meters < 1000) {
      return '$meters m';
    } else {
      final km = meters / 1000.0;
      return '${km.toStringAsFixed(1)} km';
    }
  }
}

void main() {
  group('JourneyStats', () {
    test('constructor initializes all properties correctly', () {
      final DateTime testTime = DateTime(2023, 5, 15, 10, 30);
      final stats = MockJourneyStats(
        id: 'test-id-123',
        completedAt: testTime,
        routeName: 'Test Route',
        startAddress: 'London',
        endAddress: 'Cambridge',
        distance: '80.5 km',
        distanceValue: 80500,
        duration: '1h 15m',
        durationValue: 4500,
        emissions: 12.5,
        fuelConsumption: 5.6,
        fuelCost: 8.4,
        actualDuration: '1h 20m',
        actualDurationSeconds: 4800,
        fastestRouteEmissions: 15.0,
        fastestRouteFuelConsumption: 6.7,
        fastestRouteFuelCost: 10.05,
        emissionsSaved: 2.5,
        fuelSaved: 1.1,
        costSaved: 1.65,
      );

      expect(stats.id, equals('test-id-123'));
      expect(stats.completedAt, equals(testTime));
      expect(stats.routeName, equals('Test Route'));
      expect(stats.startAddress, equals('London'));
      expect(stats.endAddress, equals('Cambridge'));
      expect(stats.distance, equals('80.5 km'));
      expect(stats.distanceValue, equals(80500));
      expect(stats.duration, equals('1h 15m'));
      expect(stats.durationValue, equals(4500));
      expect(stats.emissions, equals(12.5));
      expect(stats.fuelConsumption, equals(5.6));
      expect(stats.fuelCost, equals(8.4));
      expect(stats.actualDuration, equals('1h 20m'));
      expect(stats.actualDurationSeconds, equals(4800));
      expect(stats.fastestRouteEmissions, equals(15.0));
      expect(stats.fastestRouteFuelConsumption, equals(6.7));
      expect(stats.fastestRouteFuelCost, equals(10.05));
      expect(stats.emissionsSaved, equals(2.5));
      expect(stats.fuelSaved, equals(1.1));
      expect(stats.costSaved, equals(1.65));
    });

    test('fromJson creates correct object', () {
      final json = {
        'id': 'test-id-123',
        'completedAt': '2023-05-15T10:30:00.000',
        'routeName': 'Test Route',
        'startAddress': 'London',
        'endAddress': 'Cambridge',
        'distance': '80.5 km',
        'distanceValue': 80500,
        'duration': '1h 15m',
        'durationValue': 4500,
        'emissions': 12.5,
        'fuelConsumption': 5.6,
        'fuelCost': 8.4,
        'actualDuration': '1h 20m',
        'actualDurationSeconds': 4800,
        'fastestRouteEmissions': 15.0,
        'fastestRouteFuelConsumption': 6.7,
        'fastestRouteFuelCost': 10.05,
        'emissionsSaved': 2.5,
        'fuelSaved': 1.1,
        'costSaved': 1.65,
      };

      final stats = MockJourneyStats.fromJson(json);

      expect(stats.id, equals('test-id-123'));
      expect(stats.completedAt, equals(DateTime(2023, 5, 15, 10, 30)));
      expect(stats.routeName, equals('Test Route'));
      expect(stats.emissions, equals(12.5));
      expect(stats.emissionsSaved, equals(2.5));
    });

    test('toJson creates correct map', () {
      final DateTime testTime = DateTime(2023, 5, 15, 10, 30);
      final stats = MockJourneyStats(
        id: 'test-id-123',
        completedAt: testTime,
        routeName: 'Test Route',
        startAddress: 'London',
        endAddress: 'Cambridge',
        distance: '80.5 km',
        distanceValue: 80500,
        duration: '1h 15m',
        durationValue: 4500,
        emissions: 12.5,
        fuelConsumption: 5.6,
        fuelCost: 8.4,
        actualDuration: '1h 20m',
        actualDurationSeconds: 4800,
        fastestRouteEmissions: 15.0,
        fastestRouteFuelConsumption: 6.7,
        fastestRouteFuelCost: 10.05,
        emissionsSaved: 2.5,
        fuelSaved: 1.1,
        costSaved: 1.65,
      );

      final json = stats.toJson();

      expect(json['id'], equals('test-id-123'));
      expect(json['completedAt'], equals('2023-05-15T10:30:00.000'));
      expect(json['routeName'], equals('Test Route'));
      expect(json['emissions'], equals(12.5));
      expect(json['emissionsSaved'], equals(2.5));
    });

    test('equality operator works correctly', () {
      final stats1 = MockJourneyStats(
        id: 'test-id-123',
        completedAt: DateTime(2023, 5, 15, 10, 30),
        routeName: 'Test Route',
        startAddress: 'London',
        endAddress: 'Cambridge',
        distance: '80.5 km',
        distanceValue: 80500,
        duration: '1h 15m',
        durationValue: 4500,
        emissions: 12.5,
        fuelConsumption: 5.6,
        fuelCost: 8.4,
        actualDuration: '1h 20m',
        actualDurationSeconds: 4800,
      );

      final stats2 = MockJourneyStats(
        id: 'test-id-123', // Same ID
        completedAt: DateTime(2023, 5, 15, 11, 30), // Different time
        routeName: 'Different Route', // Different name
        startAddress: 'London',
        endAddress: 'Cambridge',
        distance: '80.5 km',
        distanceValue: 80500,
        duration: '1h 15m',
        durationValue: 4500,
        emissions: 12.5,
        fuelConsumption: 5.6,
        fuelCost: 8.4,
        actualDuration: '1h 20m',
        actualDurationSeconds: 4800,
      );

      final stats3 = MockJourneyStats(
        id: 'different-id', // Different ID
        completedAt: DateTime(2023, 5, 15, 10, 30),
        routeName: 'Test Route',
        startAddress: 'London',
        endAddress: 'Cambridge',
        distance: '80.5 km',
        distanceValue: 80500,
        duration: '1h 15m',
        durationValue: 4500,
        emissions: 12.5,
        fuelConsumption: 5.6,
        fuelCost: 8.4,
        actualDuration: '1h 20m',
        actualDurationSeconds: 4800,
      );

      expect(stats1 == stats2, isTrue); // Same ID, should be equal
      expect(stats1 == stats3, isFalse); // Different ID, should not be equal
    });
  });

  group('AggregatedJourneyStats', () {
    test('formatDuration formats seconds correctly', () {
      expect(MockAggregatedJourneyStats.formatDuration(0), equals(''));
      expect(MockAggregatedJourneyStats.formatDuration(30), equals('30s'));
      expect(MockAggregatedJourneyStats.formatDuration(90), equals('1m 30s'));
      expect(MockAggregatedJourneyStats.formatDuration(3600), equals('1h'));
      expect(MockAggregatedJourneyStats.formatDuration(3661), equals('1h 1m 1s'));
      expect(MockAggregatedJourneyStats.formatDuration(7200), equals('2h'));
    });

    test('formatDistance formats meters correctly', () {
      expect(MockAggregatedJourneyStats.formatDistance(500), equals('500 m'));
      expect(MockAggregatedJourneyStats.formatDistance(1000), equals('1.0 km'));
      expect(MockAggregatedJourneyStats.formatDistance(1500), equals('1.5 km'));
      expect(MockAggregatedJourneyStats.formatDistance(10500), equals('10.5 km'));
    });
  });
}
