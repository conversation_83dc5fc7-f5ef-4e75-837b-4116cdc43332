import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/journey_stats.dart';
import '../services/journey_stats_service.dart';

class JourneyStatsScreen extends StatefulWidget {
  const JourneyStatsScreen({super.key});

  @override
  JourneyStatsScreenState createState() => JourneyStatsScreenState();
}

class JourneyStatsScreenState extends State<JourneyStatsScreen> {
  bool _isLoading = true;
  AggregatedJourneyStats? _aggregatedStats;
  List<JourneyStats> _journeyStats = [];

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final aggregatedStats = await JourneyStatsService.getAggregatedStats();
      final journeyStats = await JourneyStatsService.getJourneyStats();

      // Sort journey stats by completion date (newest first)
      journeyStats.sort((a, b) => b.completedAt.compareTo(a.completedAt));

      setState(() {
        _aggregatedStats = aggregatedStats;
        _journeyStats = journeyStats;
        _isLoading = false;
      });
    } catch (e) {
      // Error handled by updating UI state
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Journey Statistics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStats,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildStatsContent(),
    );
  }

  Widget _buildStatsContent() {
    if (_aggregatedStats == null) {
      return const Center(
        child: Text('No statistics available'),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary card
          _buildSummaryCard(),

          // Recent journeys
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Journey History (${_journeyStats.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          _journeyStats.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32.0),
                    child: Text(
                      'No journeys completed yet',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _journeyStats.length,
                  itemBuilder: (context, index) {
                    return _buildJourneyCard(_journeyStats[index]);
                  },
                ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final stats = _aggregatedStats!;

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Total Statistics',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Total journeys
            Row(
              children: [
                const Icon(Icons.route, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Journeys Completed: ${stats.totalJourneys}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total distance
            Row(
              children: [
                const Icon(Icons.straighten, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Total Distance: ${stats.totalDistance}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total time
            Row(
              children: [
                const Icon(Icons.timer, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Total Navigation Time: ${stats.totalActualDuration}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total emissions
            Row(
              children: [
                const Icon(Icons.eco, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Total Emissions: ${stats.totalEmissions.toStringAsFixed(1)} kg CO₂',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total fuel
            Row(
              children: [
                const Icon(Icons.local_gas_station, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Total Fuel Used: ${stats.totalFuelConsumption.toStringAsFixed(1)} L',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total cost
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Total Fuel Cost: £${stats.totalFuelCost.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            // Add a divider before savings section
            const Divider(height: 32),

            // Savings section title
            const Text(
              'Eco-Route Savings',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            const SizedBox(height: 16),

            // Emissions saved
            Row(
              children: [
                Icon(
                  Icons.eco,
                  color: stats.totalEmissionsSaved > 0 ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Emissions Saved: ${stats.totalEmissionsSaved.toStringAsFixed(1)} kg CO₂',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: stats.totalEmissionsSaved > 0 ? Colors.green.shade800 : Colors.red.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Fuel saved
            Row(
              children: [
                Icon(
                  Icons.local_gas_station,
                  color: stats.totalFuelSaved > 0 ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Fuel Saved: ${stats.totalFuelSaved.toStringAsFixed(1)} L',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: stats.totalFuelSaved > 0 ? Colors.green.shade800 : Colors.red.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Money saved
            Row(
              children: [
                Icon(
                  Icons.savings,
                  color: stats.totalCostSaved > 0 ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Money Saved: £${stats.totalCostSaved.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: stats.totalCostSaved > 0 ? Colors.green.shade800 : Colors.red.shade800,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJourneyCard(JourneyStats journey) {
    final dateFormat = DateFormat('MMM d, yyyy • h:mm a');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ExpansionTile(
        title: Text(
          journey.routeName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              dateFormat.format(journey.completedAt),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${journey.distance} • ${journey.actualDuration}',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('From: ${journey.startAddress}'),
                const SizedBox(height: 4),
                Text('To: ${journey.endAddress}'),
                const SizedBox(height: 8),
                const Divider(),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Estimated Time:',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Text(journey.duration),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Actual Time:',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Text(journey.actualDuration),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Emissions:',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Text('${journey.emissions.toStringAsFixed(1)} kg CO₂'),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Fuel Used:',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Text('${journey.fuelConsumption.toStringAsFixed(1)} L'),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Fuel Cost:',
                      style: TextStyle(color: Colors.grey),
                    ),
                    Text('£${journey.fuelCost.toStringAsFixed(2)}'),
                  ],
                ),

                // Add savings information if available
                if (journey.emissionsSaved != 0 || journey.fuelSaved != 0 || journey.costSaved != 0)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 8),
                      const Text(
                        'Compared to Fastest Route:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Emissions Saved:',
                                style: TextStyle(color: Colors.grey),
                              ),
                              Text(
                                '${journey.emissionsSaved.toStringAsFixed(1)} kg CO₂',
                                style: TextStyle(
                                  color: journey.emissionsSaved > 0
                                      ? Colors.green.shade800
                                      : Colors.red.shade800,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Fuel Saved:',
                                style: TextStyle(color: Colors.grey),
                              ),
                              Text(
                                '${journey.fuelSaved.toStringAsFixed(1)} L',
                                style: TextStyle(
                                  color: journey.fuelSaved > 0
                                      ? Colors.green.shade800
                                      : Colors.red.shade800,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Money Saved:',
                            style: TextStyle(color: Colors.grey),
                          ),
                          Text(
                            '£${journey.costSaved.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: journey.costSaved > 0
                                  ? Colors.green.shade800
                                  : Colors.red.shade800,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
