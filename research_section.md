# 3.2 Research

This section outlines the research conducted to identify, evaluate, and implement the appropriate technologies and methodologies for the eco_route application. The research focused primarily on the Google Maps Platform and its various APIs, as well as environmental impact modeling for different vehicle types.

## 3.2.1 Google Maps Platform Research

The Google Maps Platform was selected as the foundation for the eco_route application due to its comprehensive suite of location-based services, extensive documentation, and robust developer support. The platform offers a variety of APIs that enable different mapping functionalities, each serving a specific purpose in the application.

### Google Maps Platform Overview

Google Maps Platform is a cloud-based mapping solution that provides developers with tools to embed maps, navigation, and location intelligence into applications. The platform is divided into three main product areas:

1. **Maps**: For creating customized maps with styles, markers, and interactive elements
2. **Routes**: For calculating directions, distances, and travel times between locations
3. **Places**: For accessing information about locations, businesses, and points of interest

For the eco_route application, I conducted extensive research into each of these product areas to determine which specific APIs would best support the application's requirements for eco-friendly routing.

### API Selection Criteria

When evaluating the Google Maps Platform APIs, I considered several key factors:

1. **Functionality**: The ability to provide the specific data needed for eco-friendly routing
2. **Accuracy**: The precision of the data, particularly for traffic conditions and elevation
3. **Performance**: Response times and reliability under various conditions
4. **Cost**: API usage pricing and potential optimization strategies
5. **Integration Complexity**: Ease of implementation within a Flutter application

Based on these criteria, I selected four primary APIs from the Google Maps Platform for implementation in the eco_route application.

## 3.2.2 Google Maps SDK for Flutter

The Google Maps SDK for Flutter was the first component researched and implemented. This SDK provides a Flutter plugin that enables embedding Google Maps within the application.

### Key Findings

1. **Native Integration**: The SDK provides a `GoogleMap` widget that renders maps using native platform views, ensuring optimal performance on both Android and iOS.

2. **Customization Options**: The SDK supports custom map styles, markers, polylines, and camera controls, allowing for a tailored user experience.

3. **Event Handling**: The SDK provides callbacks for user interactions such as taps, drags, and camera movements, enabling responsive map interactions.

4. **Implementation Considerations**: The research revealed that proper configuration is required in platform-specific files:
   - Android: API key configuration in `AndroidManifest.xml`
   - iOS: API key setup in `AppDelegate.swift` and location permission settings in `Info.plist`

The Google Maps SDK serves as the visual foundation of the eco_route application, displaying routes, traffic conditions, and user locations.

## 3.2.3 Google Routes API

The Routes API was identified as the most critical component for the eco_route application's core functionality. This API provides directions, distances, and travel times between locations, with support for various transportation modes and routing preferences.

### Key Findings

1. **Routing Preferences**: The API supports multiple routing preferences, including:
   - `TRAFFIC_AWARE`: Routes that account for real-time traffic conditions
   - `TRAFFIC_AWARE_OPTIMAL`: The fastest route accounting for traffic
   - `FUEL_EFFICIENT`: Routes optimized for fuel efficiency

2. **Multiple Route Options**: The API can return alternative routes through the `computeAlternativeRoutes` parameter, enabling the application to present users with different routing options.

3. **Traffic Data**: The API provides detailed traffic information through the `speedReadingIntervals` field, which indicates traffic conditions along different segments of a route.

4. **Polyline Encoding**: Routes are returned as encoded polylines, which efficiently represent the path as a series of points that can be decoded and displayed on the map.

5. **Implementation Challenges**: The research revealed that the `FUEL_EFFICIENT` routing preference must be paired with `TRAFFIC_AWARE_OPTIMAL` to function correctly, and that multiple API requests with different parameters are needed to generate diverse route options.

The Routes API implementation in eco_route involves making parallel requests with different routing preferences to generate a variety of route options, which are then processed to calculate environmental impact metrics.

## 3.2.4 Google Places API

The Places API was researched and implemented to provide address search, autocomplete, and location details functionality.

### Key Findings

1. **Geocoding**: The API supports both forward geocoding (converting addresses to coordinates) and reverse geocoding (converting coordinates to addresses).

2. **Place Autocomplete**: The API provides real-time suggestions as users type, improving the user experience when entering locations.

3. **Place Details**: The API returns comprehensive information about locations, including formatted addresses, coordinates, and place names.

4. **Implementation Considerations**: The research showed that place autocomplete requests should be throttled to avoid excessive API calls during user typing, and that place details should be cached when possible to reduce API usage.

The Places API is used in eco_route for address input, location search, and displaying location information on markers.

## 3.2.5 Google Elevation API

The Elevation API was researched to incorporate terrain data into the eco-friendly routing calculations.

### Key Findings

1. **Elevation Data**: The API provides elevation data for specified coordinates, enabling the calculation of route gradients.

2. **Batch Requests**: The API supports batch requests for up to 512 locations in a single call, allowing efficient retrieval of elevation data for entire routes.

3. **Gradient Calculation**: The research revealed that calculating meaningful gradient factors requires sampling elevation data at regular intervals along a route and computing the changes in elevation relative to distance.

4. **Implementation Challenges**: The API has usage limits that necessitate efficient sampling of route points to minimize the number of API calls while maintaining accuracy.

The Elevation API is used in eco_route to calculate a gradient factor that adjusts fuel consumption and emissions estimates based on the terrain of each route.

## 3.2.6 Environmental Impact Modeling Research

In addition to the Google Maps Platform APIs, extensive research was conducted into environmental impact modeling for different vehicle types.

### Key Findings

1. **Emissions Factors**: Research into official sources such as the UK Department for Transport and the European Environment Agency provided standardized emissions factors for different vehicle and fuel types:
   - Diesel: 0.180 kg CO2e/km, 2.66 kg CO2e/L
   - Petrol: 0.170 kg CO2e/km, 2.35 kg CO2e/L
   - Hybrid: 0.113 kg CO2e/km, 1.80 kg CO2e/L equivalent
   - Electric: 0.026 kg CO2e/km (grid-dependent at 0.193 kg CO₂e/kWh)

2. **Traffic Impact**: Research showed that traffic congestion significantly increases emissions, with stop-and-go traffic potentially doubling fuel consumption and emissions compared to free-flowing traffic.

3. **Gradient Impact**: Studies indicated that uphill driving can increase fuel consumption by 30-50%, while downhill sections can reduce consumption, resulting in a net increase over varied terrain.

4. **Vehicle-Specific Factors**: Research revealed that different vehicle types respond differently to traffic and gradient conditions:
   - Electric vehicles are less affected by traffic due to regenerative braking
   - Hybrid vehicles benefit from electric power in low-speed traffic conditions
   - Diesel engines are more efficient at steady highway speeds
   - Petrol engines are more sensitive to aggressive acceleration in traffic

This environmental research informed the development of the vehicle emissions models in the eco_route application, enabling accurate calculation of emissions and fuel consumption for different route options.

## 3.2.7 Research Conclusion

The research into Google Maps Platform APIs and environmental impact modeling provided a solid foundation for implementing the eco_route application. The selected APIs offer comprehensive functionality for mapping, routing, location search, and elevation data, while the environmental research enabled the development of accurate emissions and fuel consumption models.

The combination of these technologies and methodologies allows eco_route to provide users with multiple route options, each with detailed environmental impact metrics, enabling informed decisions about eco-friendly travel.
