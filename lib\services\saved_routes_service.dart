import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/saved_route.dart';
import '../services/routing_service.dart';

/// Service for managing saved routes using SharedPreferences for local storage
class SavedRoutesService {
  static const String _localRoutesKey = 'local_saved_routes';

  /// Save a route locally
  static Future<bool> saveRouteLocally({
    required String name,
    required String startAddress,
    required String endAddress,
    required LatLng startLocation,
    required LatLng endLocation,
    required RouteInfo routeInfo,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing routes
      final List<String> savedRoutesJson = prefs.getStringList(_localRoutesKey) ?? [];
      final List<SavedRoute> savedRoutes = savedRoutesJson
          .map((json) => SavedRoute.fromMap(jsonDecode(json)))
          .toList();

      // Create a new route
      final newRoute = SavedRoute.fromRouteInfo(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        startAddress: startAddress,
        endAddress: endAddress,
        startLocation: startLocation,
        endLocation: endLocation,
        routeInfo: routeInfo,
        userId: '', // No user ID needed
      );

      // Add the new route
      savedRoutes.add(newRoute);

      // Save back to SharedPreferences
      final updatedRoutesJson = savedRoutes
          .map((route) => jsonEncode(route.toMap()))
          .toList();

      return await prefs.setStringList(_localRoutesKey, updatedRoutesJson);
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  /// Get all locally saved routes
  static Future<List<SavedRoute>> getLocalRoutes() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing routes
      final List<String> savedRoutesJson = prefs.getStringList(_localRoutesKey) ?? [];

      // Convert to SavedRoute objects
      return savedRoutesJson
          .map((json) => SavedRoute.fromMap(jsonDecode(json)))
          .toList();
    } catch (e) {
      // Error handled by returning empty list
      return [];
    }
  }

  /// Delete a locally saved route
  static Future<bool> deleteLocalRoute(String routeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing routes
      final List<String> savedRoutesJson = prefs.getStringList(_localRoutesKey) ?? [];
      final List<SavedRoute> savedRoutes = savedRoutesJson
          .map((json) => SavedRoute.fromMap(jsonDecode(json)))
          .toList();

      // Remove the route with the given ID
      savedRoutes.removeWhere((route) => route.id == routeId);

      // Save back to SharedPreferences
      final updatedRoutesJson = savedRoutes
          .map((route) => jsonEncode(route.toMap()))
          .toList();

      return await prefs.setStringList(_localRoutesKey, updatedRoutesJson);
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }


}
