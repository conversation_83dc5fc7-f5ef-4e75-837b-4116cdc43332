import 'package:flutter_test/flutter_test.dart';
import 'package:eco_route/models/vehicle_info.dart';

// Mock implementation of VehicleInfo for testing
class MockVehicleInfo {
  EngineType engineType;
  double fuelConsumption;
  double fuelPrice;
  double electricityPrice;
  double? customEmissionsRate;

  static const double defaultElectricityPrice = 0.27;

  static const Map<EngineType, double> defaultEmissionsPerKm = {
    EngineType.diesel: 0.180, // kg CO2e per km
    EngineType.petrol: 0.170, // kg CO2e per km
    EngineType.hybrid: 0.113, // kg CO2e per km
    EngineType.electric: 0.026, // kg CO2e per km (grid dependent)
  };

  MockVehicleInfo({
    this.engineType = EngineType.petrol,
    this.fuelConsumption = 8.0,
    this.fuelPrice = 1.50,
    this.electricityPrice = defaultElectricityPrice,
    this.customEmissionsRate,
  });

  double calculateEmissions(double distanceKm, [double gradientFactor = 1.0, double trafficFactor = 1.0]) {
    // If user has provided a custom emissions rate, use that directly
    if (customEmissionsRate != null) {
      // Apply gradient factor with reduced effect for electric vehicles
      if (engineType == EngineType.electric) {
        double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
        double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
        return distanceKm * customEmissionsRate! * electricGradientFactor * electricTrafficFactor;
      } else {
        return distanceKm * customEmissionsRate! * gradientFactor * trafficFactor;
      }
    }

    // Otherwise use the default calculation based on engine type
    double baseEmissions = defaultEmissionsPerKm[engineType]! * distanceKm;

    // Apply different factors based on engine type
    if (engineType == EngineType.electric) {
      // Electric vehicles are less affected by gradient and traffic
      double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
      double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
      return baseEmissions * electricGradientFactor * electricTrafficFactor;
    } else {
      return baseEmissions * gradientFactor * trafficFactor;
    }
  }

  double calculateFuelCost(double distanceKm, [double gradientFactor = 1.0, double trafficFactor = 1.0]) {
    // For electric vehicles
    if (engineType == EngineType.electric) {
      // Electric vehicles are less affected by gradient
      double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
      double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);

      // kWh = (distance in km / 100) * consumption in kWh/100km * factors
      double kWh = (distanceKm / 100) * fuelConsumption * electricGradientFactor * electricTrafficFactor;
      return kWh * electricityPrice;
    }

    // For hybrid vehicles
    if (engineType == EngineType.hybrid) {
      // Assume 70% fuel, 30% electric for hybrid
      double fuelPortion = (distanceKm / 100) * fuelConsumption * 0.7 * gradientFactor * trafficFactor;
      double fuelCost = fuelPortion * fuelPrice;

      // Electric portion with reduced gradient factor
      double electricGradientFactor = 1.0 + ((gradientFactor - 1.0) * 0.5);
      double electricTrafficFactor = 1.0 + ((trafficFactor - 1.0) * 0.7);
      double kWh = (distanceKm * 0.3 / 100) * 10.0 * electricGradientFactor * electricTrafficFactor; // Assume 10 kWh/100km for electric portion
      double electricCost = kWh * electricityPrice;

      return fuelCost + electricCost;
    }

    // For petrol/diesel vehicles
    double liters = (fuelConsumption * distanceKm * gradientFactor * trafficFactor / 100);
    return liters * fuelPrice;
  }
}

void main() {
  group('VehicleInfo', () {
    test('default constructor initializes with correct values', () {
      final vehicleInfo = MockVehicleInfo();

      expect(vehicleInfo.engineType, equals(EngineType.petrol));
      expect(vehicleInfo.fuelConsumption, equals(8.0));
      expect(vehicleInfo.fuelPrice, equals(1.50));
      expect(vehicleInfo.electricityPrice, equals(MockVehicleInfo.defaultElectricityPrice));
      expect(vehicleInfo.customEmissionsRate, isNull);
    });

    test('constructor with parameters sets values correctly', () {
      final vehicleInfo = MockVehicleInfo(
        engineType: EngineType.electric,
        fuelConsumption: 20.0,
        fuelPrice: 0.0,
        electricityPrice: 0.30,
        customEmissionsRate: 0.05,
      );

      expect(vehicleInfo.engineType, equals(EngineType.electric));
      expect(vehicleInfo.fuelConsumption, equals(20.0));
      expect(vehicleInfo.fuelPrice, equals(0.0));
      expect(vehicleInfo.electricityPrice, equals(0.30));
      expect(vehicleInfo.customEmissionsRate, equals(0.05));
    });

    group('calculateEmissions', () {
      test('calculates emissions correctly for petrol vehicles', () {
        final vehicleInfo = MockVehicleInfo(engineType: EngineType.petrol);

        // Basic calculation: 10km * 0.170 kg/km = 1.70 kg
        expect(vehicleInfo.calculateEmissions(10.0), closeTo(1.70, 0.01));

        // With gradient factor: 10km * 0.170 kg/km * 1.5 = 2.55 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.5), closeTo(2.55, 0.01));

        // With traffic factor: 10km * 0.170 kg/km * 1.3 = 2.21 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.0, 1.3), closeTo(2.21, 0.01));

        // With both factors: 10km * 0.170 kg/km * 1.5 * 1.3 = 3.315 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.5, 1.3), closeTo(3.315, 0.01));
      });

      test('calculates emissions correctly for diesel vehicles', () {
        final vehicleInfo = MockVehicleInfo(engineType: EngineType.diesel);

        // Basic calculation: 10km * 0.180 kg/km = 1.80 kg
        expect(vehicleInfo.calculateEmissions(10.0), closeTo(1.80, 0.01));

        // With gradient factor: 10km * 0.180 kg/km * 1.5 = 2.70 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.5), closeTo(2.70, 0.01));
      });

      test('calculates emissions correctly for hybrid vehicles', () {
        final vehicleInfo = MockVehicleInfo(engineType: EngineType.hybrid);

        // Basic calculation: 10km * 0.113 kg/km = 1.13 kg
        expect(vehicleInfo.calculateEmissions(10.0), closeTo(1.13, 0.01));
      });

      test('calculates emissions correctly for electric vehicles', () {
        final vehicleInfo = MockVehicleInfo(engineType: EngineType.electric);

        // Basic calculation: 10km * 0.026 kg/km = 0.26 kg
        expect(vehicleInfo.calculateEmissions(10.0), closeTo(0.26, 0.01));

        // Electric vehicles have reduced sensitivity to gradient and traffic
        // With gradient factor 1.5: 10km * 0.026 kg/km * (1.0 + (1.5-1.0)*0.5) = 0.26 * 1.25 = 0.325 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.5), closeTo(0.325, 0.01));

        // With traffic factor 1.3: 10km * 0.026 kg/km * (1.0 + (1.3-1.0)*0.7) = 0.26 * 1.21 = 0.3146 kg
        expect(vehicleInfo.calculateEmissions(10.0, 1.0, 1.3), closeTo(0.3146, 0.01));
      });

      test('uses custom emissions rate when provided', () {
        final vehicleInfo = MockVehicleInfo(
          engineType: EngineType.petrol,
          customEmissionsRate: 0.2, // Custom rate of 0.2 kg/km
        );

        // Basic calculation with custom rate: 10km * 0.2 kg/km = 2.0 kg
        expect(vehicleInfo.calculateEmissions(10.0), closeTo(2.0, 0.01));
      });
    });

    group('calculateFuelCost', () {
      test('calculates fuel cost correctly for petrol vehicles', () {
        final vehicleInfo = MockVehicleInfo(
          engineType: EngineType.petrol,
          fuelConsumption: 7.0, // 7 L/100km
          fuelPrice: 1.50, // £1.50 per litre
        );

        // Basic calculation: (7 L/100km * 10km / 100) * £1.50/L = 0.7L * £1.50/L = £1.05
        expect(vehicleInfo.calculateFuelCost(10.0), closeTo(1.05, 0.01));

        // With gradient factor: (7 L/100km * 10km * 1.5 / 100) * £1.50/L = 1.05L * £1.50/L = £1.575
        expect(vehicleInfo.calculateFuelCost(10.0, 1.5), closeTo(1.575, 0.01));
      });

      test('calculates fuel cost correctly for electric vehicles', () {
        final vehicleInfo = MockVehicleInfo(
          engineType: EngineType.electric,
          fuelConsumption: 15.0, // 15 kWh/100km
          electricityPrice: 0.27, // £0.27 per kWh
        );

        // Basic calculation: (10km / 100) * 15 kWh/100km * £0.27/kWh = 0.405
        expect(vehicleInfo.calculateFuelCost(10.0), closeTo(0.405, 0.01));

        // Electric vehicles have reduced sensitivity to gradient
        // With gradient factor 1.5: (10km / 100) * 15 kWh/100km * (1.0 + (1.5-1.0)*0.5) * £0.27/kWh = 0.50625
        expect(vehicleInfo.calculateFuelCost(10.0, 1.5), closeTo(0.50625, 0.01));
      });

      test('calculates fuel cost correctly for hybrid vehicles', () {
        final vehicleInfo = MockVehicleInfo(
          engineType: EngineType.hybrid,
          fuelConsumption: 4.0, // 4 L/100km
          fuelPrice: 1.50, // £1.50 per litre
          electricityPrice: 0.27, // £0.27 per kWh
        );

        // Hybrid calculation combines fuel and electricity costs
        // Fuel portion: (4 L/100km * 10km * 0.7 / 100) * £1.50/L = 0.42
        // Electric portion: (10km * 0.3 / 100) * 10 kWh/100km * £0.27/kWh = 0.081
        // Total: £0.42 + £0.081 = £0.501
        expect(vehicleInfo.calculateFuelCost(10.0), closeTo(0.501, 0.01));
      });
    });
  });
}
