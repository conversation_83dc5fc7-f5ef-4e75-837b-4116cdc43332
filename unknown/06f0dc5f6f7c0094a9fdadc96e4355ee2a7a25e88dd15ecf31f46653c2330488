import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/routing_service.dart';

/// Model class for a saved route
class SavedRoute {
  final String id; // Unique identifier
  final String name; // User-provided name
  final String startAddress;
  final String endAddress;
  final LatLng startLocation;
  final LatLng endLocation;
  final String encodedPolyline; // Encoded polyline for the route
  final String distance;
  final String duration;
  final double distanceValue; // Distance in kilometers
  final int durationValue; // Duration in seconds
  final RouteType routeType; // Type of route (fastest, eco-friendly, etc.)
  final DateTime createdAt; // When the route was saved
  final String userId; // Legacy field, kept for compatibility

  SavedRoute({
    required this.id,
    required this.name,
    required this.startAddress,
    required this.endAddress,
    required this.startLocation,
    required this.endLocation,
    required this.encodedPolyline,
    required this.distance,
    required this.duration,
    required this.distanceValue,
    required this.durationValue,
    required this.routeType,
    required this.createdAt,
    required this.userId,
  });

  /// Create a SavedRoute from a RouteInfo object
  static SavedRoute fromRouteInfo({
    required String id,
    required String name,
    required String startAddress,
    required String endAddress,
    required LatLng startLocation,
    required LatLng endLocation,
    required RouteInfo routeInfo,
    required String userId,
  }) {
    return SavedRoute(
      id: id,
      name: name,
      startAddress: startAddress,
      endAddress: endAddress,
      startLocation: startLocation,
      endLocation: endLocation,
      encodedPolyline: routeInfo.points,
      distance: routeInfo.distance,
      duration: routeInfo.duration,
      distanceValue: routeInfo.distanceValue,
      durationValue: routeInfo.durationValue,
      routeType: routeInfo.routeType,
      createdAt: DateTime.now(),
      userId: userId,
    );
  }

  /// Convert to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'startAddress': startAddress,
      'endAddress': endAddress,
      'startLocation': {
        'latitude': startLocation.latitude,
        'longitude': startLocation.longitude,
      },
      'endLocation': {
        'latitude': endLocation.latitude,
        'longitude': endLocation.longitude,
      },
      'encodedPolyline': encodedPolyline,
      'distance': distance,
      'duration': duration,
      'distanceValue': distanceValue,
      'durationValue': durationValue,
      'routeType': routeType.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'userId': userId,
    };
  }

  /// Create a SavedRoute from a map
  factory SavedRoute.fromMap(Map<String, dynamic> map) {
    return SavedRoute(
      id: map['id'],
      name: map['name'],
      startAddress: map['startAddress'],
      endAddress: map['endAddress'],
      startLocation: LatLng(
        map['startLocation']['latitude'],
        map['startLocation']['longitude'],
      ),
      endLocation: LatLng(
        map['endLocation']['latitude'],
        map['endLocation']['longitude'],
      ),
      encodedPolyline: map['encodedPolyline'],
      distance: map['distance'],
      duration: map['duration'],
      distanceValue: map['distanceValue'],
      durationValue: map['durationValue'],
      routeType: RouteType.values[map['routeType']],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      userId: map['userId'],
    );
  }
}
