# Functional Requirements Table

| ID | Requirement Category | Functional Requirement | Technical Implementation | Priority | Justification |
|----|---------------------|------------------------|--------------------------|----------|---------------|
| FR1 | Vehicle Configuration | Allow users to select engine type (Diesel, Petrol, Hybrid, Electric) and customize fuel consumption parameters | Implemented through the `VehicleInfo` class with engine-specific emissions factors and UI controls in the vehicle settings sidebar | High | Essential for accurate emissions calculations as different engine types have significantly different environmental impacts. The UK Department for Transport and European Environment Agency data shows emissions vary from 0.026 kg CO2e/km for electric vehicles to 0.180 kg CO2e/km for diesel vehicles. |
| FR2 | Route Input | Allow users to select start point and destination via address search, map pins, or current location | Implemented using Google Places API for address autocomplete, geocoding for coordinate conversion, and Geolocator for current location detection | High | Fundamental to the application's core functionality, allowing users to specify journey endpoints in multiple intuitive ways. Research shows that flexible input methods significantly improve user experience in navigation applications. |
| FR3 | Emissions Calculation | Compute CO2e emissions for routes using vehicle-specific factors, adjusting for gradient and traffic conditions | Implemented in the `VehicleInfo.calculateEmissions()` method using standardized emissions factors (kg CO2e/km) with gradient and traffic multipliers | High | Core to the application's purpose of providing accurate environmental impact information. Research shows that traffic congestion can double emissions, while uphill driving can increase fuel consumption by 30-50%. |
| FR4 | Multiple Route Options | Display multiple route alternatives with different environmental impacts | Implemented by making parallel requests to the Google Routes API with different routing preferences (standard routes and routes avoiding highways) | High | Essential for allowing users to make informed choices. Research shows that alternative routes can have significantly different emissions profiles even when travel times are similar. |
| FR5 | Route Classification | Identify and label routes as "eco route," "fastest route," or "fastest + eco route" | Implemented through the `RouteType` enum and route comparison logic in `RoutingService.calculateRoutes()` | High | Provides clear decision-making guidance to users by highlighting the environmental trade-offs between different routes. |
| FR6 | Traffic Integration | Adjust emissions calculations based on real-time traffic conditions | Implemented using the Google Routes API's `speedReadingIntervals` data to calculate a traffic factor that adjusts fuel consumption and emissions | Medium | Traffic conditions significantly impact fuel consumption and emissions. Stop-and-go traffic can double fuel consumption compared to free-flowing traffic. |
| FR7 | Gradient Integration | Factor road slopes into emissions calculations | Implemented using the Google Elevation API to calculate a gradient factor that adjusts fuel consumption and emissions based on terrain | High | Uphill driving can increase fuel consumption by 30-50%, making this a critical factor for accurate emissions estimates. |
| FR8 | Route Display | Show emissions, fuel consumption, and cost information for each route | Implemented through custom UI components in the bottom sheet that display environmental metrics for the selected route | High | Provides the key information users need to make environmentally conscious routing decisions. |
| FR9 | Route Saving | Allow users to save frequently used routes for quick access | Implemented using SharedPreferences for local storage of route information in the `SavedRoutesService` | Medium | Improves user experience by eliminating the need to re-enter common destinations. |
| FR10 | Journey Completion | Track actual journey duration and calculate final statistics | Implemented in the `JourneyScreen` with a timer and completion button | Medium | Provides valuable feedback on the accuracy of estimated journey times and allows for more accurate historical data. |
| FR11 | Emissions and Fuel Savings | Calculate and display emissions, fuel, and cost savings compared to the fastest route | Implemented in the `JourneyStatsService.createJourneyStats()` method by comparing the selected route's metrics with those of the fastest route | High | Quantifies the environmental and financial benefits of choosing eco-friendly routes, reinforcing positive user behavior. |
| FR12 | Statistics Dashboard | Show aggregated journey statistics including total emissions, fuel consumption, and savings | Implemented in the `JourneyStatsScreen` using the `AggregatedJourneyStats` model | Medium | Provides users with a sense of their cumulative environmental impact and the benefits of using the application over time. |
| FR13 | Vehicle-Specific Adjustments | Apply different adjustment factors for different vehicle types in response to gradient and traffic | Implemented in the `calculateEmissions()` and `calculateFuelCost()` methods with reduced gradient and traffic sensitivity for electric and hybrid vehicles | High | Research shows that different vehicle types respond differently to conditions: electric vehicles benefit from regenerative braking in traffic, while diesel engines are more efficient at steady highway speeds. |
| FR14 | Custom Emissions Input | Allow users to input custom emissions rates for their specific vehicle | Implemented through the `customEmissionsRate` property in the `VehicleInfo` class | Low | Provides flexibility for users with detailed knowledge of their vehicle's specific emissions profile, enhancing accuracy for environmentally conscious users. |
| FR15 | Journey History | Maintain a history of completed journeys with their environmental metrics | Implemented using SharedPreferences for local storage in the `JourneyStatsService` | Medium | Allows users to track their environmental impact over time and identify patterns in their travel behavior. |
