import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/journey_stats.dart';

class JourneyStatsService {
  static const String _journeyStatsKey = 'journey_stats';

  // Save a new journey statistic
  static Future<bool> saveJourneyStats(JourneyStats stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing stats
      final List<JourneyStats> existingStats = await getJourneyStats();

      // Add new stats
      existingStats.add(stats);

      // Convert to JSON string list
      final List<String> jsonList = existingStats.map((stat) => jsonEncode(stat.toJson())).toList();

      // Save to SharedPreferences
      return await prefs.setStringList(_journeyStatsKey, jsonList);
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  // Get all journey statistics
  static Future<List<JourneyStats>> getJourneyStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get JSON string list
      final List<String>? jsonList = prefs.getStringList(_journeyStatsKey);

      if (jsonList == null || jsonList.isEmpty) {
        return [];
      }

      // Convert to JourneyStats objects
      return jsonList.map((jsonStr) {
        final Map<String, dynamic> json = jsonDecode(jsonStr);
        return JourneyStats.fromJson(json);
      }).toList();
    } catch (e) {
      // Error handled by returning empty list
      return [];
    }
  }

  // Create a new journey statistic
  static JourneyStats createJourneyStats({
    required String routeName,
    required String startAddress,
    required String endAddress,
    required String distance,
    required int distanceValue,
    required String duration,
    required int durationValue,
    required double emissions,
    required double fuelConsumption,
    required double fuelCost,
    required String actualDuration,
    required int actualDurationSeconds,
    double fastestRouteEmissions = 0.0,
    double fastestRouteFuelConsumption = 0.0,
    double fastestRouteFuelCost = 0.0,
  }) {
    // Calculate savings compared to fastest route
    // Positive values mean savings, negative values mean additional cost
    final emissionsSaved = fastestRouteEmissions - emissions;
    final fuelSaved = fastestRouteFuelConsumption - fuelConsumption;
    final costSaved = fastestRouteFuelCost - fuelCost;

    return JourneyStats(
      id: const Uuid().v4(),
      completedAt: DateTime.now(),
      routeName: routeName,
      startAddress: startAddress,
      endAddress: endAddress,
      distance: distance,
      distanceValue: distanceValue,
      duration: duration,
      durationValue: durationValue,
      emissions: emissions,
      fuelConsumption: fuelConsumption,
      fuelCost: fuelCost,
      actualDuration: actualDuration,
      actualDurationSeconds: actualDurationSeconds,
      fastestRouteEmissions: fastestRouteEmissions,
      fastestRouteFuelConsumption: fastestRouteFuelConsumption,
      fastestRouteFuelCost: fastestRouteFuelCost,
      emissionsSaved: emissionsSaved,
      fuelSaved: fuelSaved,
      costSaved: costSaved,
    );
  }

  // Get aggregated journey statistics
  static Future<AggregatedJourneyStats> getAggregatedStats() async {
    final List<JourneyStats> allStats = await getJourneyStats();

    if (allStats.isEmpty) {
      return AggregatedJourneyStats(
        totalJourneys: 0,
        totalDistanceMeters: 0,
        totalDistance: '0 km',
        totalDurationSeconds: 0,
        totalDuration: '0m',
        totalEmissions: 0,
        totalFuelConsumption: 0,
        totalFuelCost: 0,
        totalActualDurationSeconds: 0,
        totalActualDuration: '0m',
        totalEmissionsSaved: 0,
        totalFuelSaved: 0,
        totalCostSaved: 0,
      );
    }

    int totalDistanceMeters = 0;
    int totalDurationSeconds = 0;
    double totalEmissions = 0;
    double totalFuelConsumption = 0;
    double totalFuelCost = 0;
    int totalActualDurationSeconds = 0;
    double totalEmissionsSaved = 0;
    double totalFuelSaved = 0;
    double totalCostSaved = 0;

    for (final stat in allStats) {
      totalDistanceMeters += stat.distanceValue;
      totalDurationSeconds += stat.durationValue;
      totalEmissions += stat.emissions;
      totalFuelConsumption += stat.fuelConsumption;
      totalFuelCost += stat.fuelCost;
      totalActualDurationSeconds += stat.actualDurationSeconds;
      totalEmissionsSaved += stat.emissionsSaved;
      totalFuelSaved += stat.fuelSaved;
      totalCostSaved += stat.costSaved;
    }

    return AggregatedJourneyStats(
      totalJourneys: allStats.length,
      totalDistanceMeters: totalDistanceMeters,
      totalDistance: AggregatedJourneyStats.formatDistance(totalDistanceMeters),
      totalDurationSeconds: totalDurationSeconds,
      totalDuration: AggregatedJourneyStats.formatDuration(totalDurationSeconds),
      totalEmissions: totalEmissions,
      totalFuelConsumption: totalFuelConsumption,
      totalFuelCost: totalFuelCost,
      totalActualDurationSeconds: totalActualDurationSeconds,
      totalActualDuration: AggregatedJourneyStats.formatDuration(totalActualDurationSeconds),
      totalEmissionsSaved: totalEmissionsSaved,
      totalFuelSaved: totalFuelSaved,
      totalCostSaved: totalCostSaved,
    );
  }

  // Clear all journey statistics (for testing)
  static Future<bool> clearAllStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_journeyStatsKey);
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }
}
