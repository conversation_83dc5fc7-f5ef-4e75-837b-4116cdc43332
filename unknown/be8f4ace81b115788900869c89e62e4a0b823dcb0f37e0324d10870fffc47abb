# Testing Report for eco_route

## Overview

This report documents the testing approach and results for the eco_route application. The testing strategy focused on verifying the core algorithms and data models that are central to the application's eco-friendly routing capabilities.

## Testing Strategy

The testing approach for eco_route combined several methodologies:

1. **Unit Testing**: Isolated testing of core algorithms and data models
2. **White-Box Testing**: Tests designed with knowledge of the internal implementation
3. **Parameterized Testing**: Using multiple input combinations to verify behavior across different scenarios

This approach was chosen because the application's key value proposition—accurate environmental impact calculations—depends on the correctness of complex algorithms that process data from multiple sources.

## Test Implementation

### Core Algorithm Tests

The most critical components of the eco_route application are the algorithms that calculate emissions, fuel consumption, and cost based on route characteristics. These were tested using a white-box approach with parameterized test cases.

#### VehicleInfo Tests

The `VehicleInfo` class is responsible for calculating emissions and fuel costs based on vehicle characteristics and route factors. Tests for this component verified:

- Emissions calculations for different engine types (petrol, diesel, hybrid, electric)
- The impact of gradient and traffic factors on emissions
- Fuel cost calculations with various input parameters
- Special handling for electric and hybrid vehicles

Example test case:

```dart
test('calculates emissions correctly for petrol vehicles', () {
  final vehicleInfo = MockVehicleInfo(engineType: EngineType.petrol);
  
  // Basic calculation: 10km * 0.170 kg/km = 1.70 kg
  expect(vehicleInfo.calculateEmissions(10.0), closeTo(1.70, 0.01));
  
  // With gradient factor: 10km * 0.170 kg/km * 1.5 = 2.55 kg
  expect(vehicleInfo.calculateEmissions(10.0, 1.5), closeTo(2.55, 0.01));
  
  // With traffic factor: 10km * 0.170 kg/km * 1.3 = 2.21 kg
  expect(vehicleInfo.calculateEmissions(10.0, 1.0, 1.3), closeTo(2.21, 0.01));
  
  // With both factors: 10km * 0.170 kg/km * 1.5 * 1.3 = 3.315 kg
  expect(vehicleInfo.calculateEmissions(10.0, 1.5, 1.3), closeTo(3.315, 0.01));
});
```

#### Traffic Factor Tests

The traffic factor calculation is a key component that adjusts fuel consumption and emissions based on traffic conditions. Tests for this algorithm verified:

- Correct handling of different traffic distributions
- Weighted average calculations based on segment lengths
- Proper handling of edge cases (empty data, invalid inputs)
- Clamping of values to reasonable bounds

Example test case:

```dart
test('calculates traffic factor correctly for mixed traffic conditions', () {
  final speedReadings = [
    {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 50, 'speed': 'NORMAL'},
    {'startPolylinePointIndex': 50, 'endPolylinePointIndex': 75, 'speed': 'SLOW'},
    {'startPolylinePointIndex': 75, 'endPolylinePointIndex': 100, 'speed': 'TRAFFIC_JAM'}
  ];
  
  final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 100);
  
  // Expected factor: (50% × 1.0) + (25% × 1.3) + (25% × 1.6) = 1.225
  expect(result['trafficFactor'], closeTo(1.225, 0.01));
  
  final breakdown = result['breakdown'] as Map<TrafficSpeed, double>;
  expect(breakdown[TrafficSpeed.normal], closeTo(0.5, 0.01));
  expect(breakdown[TrafficSpeed.slow], closeTo(0.25, 0.01));
  expect(breakdown[TrafficSpeed.trafficJam], closeTo(0.25, 0.01));
});
```

### Data Model Tests

The `JourneyStats` model is responsible for storing and processing journey information. Tests for this component verified:

- Correct initialization of properties
- JSON serialization and deserialization
- Equality comparison based on ID

## Test Results

The testing process revealed several important insights:

1. **Emissions Calculation Accuracy**: The emissions calculations correctly account for different engine types and apply appropriate adjustments for gradient and traffic factors.

2. **Traffic Factor Calculation**: The traffic factor algorithm correctly weights different traffic conditions based on the proportion of the route affected.

3. **Engine-Specific Adjustments**: The application correctly applies different adjustment factors for electric vehicles, which are less affected by traffic and gradient changes.

## Challenges and Solutions

### Challenge: Testing Private Methods

Some critical algorithms, like the traffic factor calculation, are implemented as private methods, making them difficult to test directly.

**Solution**: Created test wrapper classes that replicate the private method implementation, allowing direct testing of the algorithm without modifying the production code.

### Challenge: Complex Parameter Combinations

The emissions and fuel cost calculations depend on multiple parameters (distance, gradient factor, traffic factor, engine type, etc.), creating a large number of possible combinations.

**Solution**: Used parameterized tests with carefully selected test cases that cover the most important combinations and edge cases.

## Conclusion

The testing approach for eco_route focused on verifying the core algorithms that are central to the application's purpose. By using a combination of unit testing and parameterized testing, we were able to verify that:

1. The emissions calculations correctly account for different vehicle types and route conditions
2. The traffic factor algorithm correctly processes speed reading data to estimate congestion impact
3. The data models correctly handle serialization and comparison operations

These tests provide confidence that the eco_route application's core functionality—providing accurate environmental impact estimates for different routes—works as expected.
