import 'package:flutter/material.dart';
import '../services/routing_service.dart';
import 'place_autocomplete.dart';

class ExpandedBottomSheet extends StatelessWidget {
  final TextEditingController startController;
  final TextEditingController destinationController;
  final VoidCallback onCalculateRoute;
  final Function(String, String)? onStartPlaceSelected;
  final Function(String, String)? onDestinationPlaceSelected;
  final VoidCallback? onUseCurrentLocation;

  const ExpandedBottomSheet({
    super.key,
    required this.startController,
    required this.destinationController,
    required this.onCalculateRoute,
    this.onStartPlaceSelected,
    this.onDestinationPlaceSelected,
    this.onUseCurrentLocation,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 0, bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              PlaceAutocomplete(
                controller: startController,
                label: 'Starting Point',
                showLocationButton: true,
                onUseCurrentLocation: onUseCurrentLocation,
                onPlaceSelected: (placeId, description) {
                  if (onStartPlaceSelected != null) {
                    onStartPlaceSelected!(placeId, description);
                  }
                },
              ),
              const SizedBox(height: 16),
              PlaceAutocomplete(
                controller: destinationController,
                label: 'Destination',
                onPlaceSelected: (placeId, description) {
                  if (onDestinationPlaceSelected != null) {
                    onDestinationPlaceSelected!(placeId, description);
                  }
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onCalculateRoute,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[800],
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text('Calculate Route'),
              ),
              // Add extra padding at the bottom to ensure content is visible above keyboard
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom > 0 ? 16 : 0),
            ],
          ),
        ),
      ),
    );
  }
}

class CollapsedBottomSheet extends StatelessWidget {
  final List<RouteInfo> routes;
  final int selectedRouteIndex;
  final Function(int) onRouteSelected;
  final VoidCallback onChangeRoute;
  final VoidCallback onSaveRoute;
  final VoidCallback onStartNavigation;

  const CollapsedBottomSheet({
    super.key,
    required this.routes,
    required this.selectedRouteIndex,
    required this.onRouteSelected,
    required this.onChangeRoute,
    required this.onSaveRoute,
    required this.onStartNavigation,
  });

  // Helper method to build traffic breakdown item
  Widget _buildTrafficBreakdownItem(String label, double percentage, Color color) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            '${(percentage * 100).toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 10,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 3,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Important to prevent overflow
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 8),

              // Route selection tabs
              if (routes.length > 1)
                SizedBox(
                  height: 60,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: routes.length,
                    itemBuilder: (context, index) {
                      final isSelected = index == selectedRouteIndex;
                      final routeInfo = routes[index];

                      return GestureDetector(
                        onTap: () => onRouteSelected(index),
                        child: Container(
                          width: 120,
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? routeInfo.routeType.color.withAlpha(51) // 0.2 opacity = 51/255
                                : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected ? routeInfo.routeType.color : Colors.grey.shade300,
                              width: 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: routeInfo.routeType.color.withAlpha(76), // 0.3 opacity = 76/255
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              )
                            ] : null,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                routeInfo.routeType.displayName,
                                style: TextStyle(
                                  color: isSelected ? routeInfo.routeType.color : Colors.grey.shade700,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                routeInfo.duration,
                                style: TextStyle(
                                  color: isSelected ? Colors.green.shade800 : Colors.grey.shade700,
                                  fontSize: 10,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),

              const SizedBox(height: 8),

              // Route details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'DISTANCE',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        routes[selectedRouteIndex].distance,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'DURATION',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        routes[selectedRouteIndex].duration,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ],
              ),

              // Eco-friendly metrics
              if (routes[selectedRouteIndex].emissions > 0)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'EMISSIONS',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                              Text(
                                '${routes[selectedRouteIndex].emissions.toStringAsFixed(1)} kg CO₂',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade800,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'FUEL',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                              Text(
                                '${routes[selectedRouteIndex].fuelConsumption.toStringAsFixed(1)} L',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade800,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'COST',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                              Text(
                                '£${routes[selectedRouteIndex].fuelCost.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade800,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      // Traffic breakdown
                      if (routes[selectedRouteIndex].trafficBreakdown != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildTrafficBreakdownItem(
                                'Normal',
                                routes[selectedRouteIndex].trafficBreakdown?[TrafficSpeed.normal] ?? 0.0,
                                Colors.green.shade700
                              ),
                              const SizedBox(width: 8),
                              _buildTrafficBreakdownItem(
                                'Slow',
                                routes[selectedRouteIndex].trafficBreakdown?[TrafficSpeed.slow] ?? 0.0,
                                Colors.orange.shade700
                              ),
                              const SizedBox(width: 8),
                              _buildTrafficBreakdownItem(
                                'Jam',
                                routes[selectedRouteIndex].trafficBreakdown?[TrafficSpeed.trafficJam] ?? 0.0,
                                Colors.red.shade700
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),

              if (routes.isNotEmpty && selectedRouteIndex < routes.length)
                Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: Column(
                    children: [
                      Text(
                        'Via ${routes[selectedRouteIndex].summary}',
                        style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${routes[selectedRouteIndex].routeType.displayName} (${selectedRouteIndex + 1} of ${routes.length})',
                        style: TextStyle(
                          color: routes[selectedRouteIndex].routeType.color,
                          fontSize: 12,
                          fontWeight: FontWeight.bold
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 8),
              // Primary action button - Start Navigation
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onStartNavigation,
                  icon: const Icon(Icons.navigation, size: 18),
                  label: const Text('Start Navigation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Secondary action buttons
              Row(
                children: [
                  // Save Route button
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 4.0),
                      child: ElevatedButton.icon(
                        onPressed: onSaveRoute,
                        icon: const Icon(Icons.bookmark_add, size: 18),
                        label: const Text('Save Route'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  // Change Route button
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 4.0),
                      child: TextButton.icon(
                        onPressed: onChangeRoute,
                        icon: const Icon(Icons.edit_road, size: 18),
                        label: const Text('Change Route'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.green,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24), // Increased padding at bottom
            ],
          ),
        ),
      ),
    );
  }
}
