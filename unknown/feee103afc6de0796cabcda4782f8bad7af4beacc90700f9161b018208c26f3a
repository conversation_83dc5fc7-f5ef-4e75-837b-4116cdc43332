import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/vehicle_info.dart';

class VehicleSettingsSidebar extends StatefulWidget {
  final VehicleInfo vehicleInfo;
  final Function(VehicleInfo) onVehicleInfoChanged;

  const VehicleSettingsSidebar({
    super.key,
    required this.vehicleInfo,
    required this.onVehicleInfoChanged,
  });

  @override
  State<VehicleSettingsSidebar> createState() => _VehicleSettingsSidebarState();
}

class _VehicleSettingsSidebarState extends State<VehicleSettingsSidebar> {
  late EngineType _selectedEngineType;
  late TextEditingController _fuelConsumptionController;
  late TextEditingController _fuelPriceController;
  late TextEditingController _electricityPriceController;
  late TextEditingController _emissionsRateController;
  bool _useCustomEmissions = false;

  @override
  void initState() {
    super.initState();
    _selectedEngineType = widget.vehicleInfo.engineType;
    _fuelConsumptionController = TextEditingController(
      text: widget.vehicleInfo.fuelConsumption.toString(),
    );
    _fuelPriceController = TextEditingController(
      text: widget.vehicleInfo.fuelPrice.toString(),
    );
    _electricityPriceController = TextEditingController(
      text: widget.vehicleInfo.electricityPrice.toString(),
    );

    // Initialize emissions rate controller with custom value if available,
    // otherwise use the default value from the table
    _emissionsRateController = TextEditingController(
      text: widget.vehicleInfo.customEmissionsRate?.toString() ??
            VehicleInfo.defaultEmissionsPerKm[widget.vehicleInfo.engineType]!.toString(),
    );

    // Set the custom emissions flag if a custom value was previously set
    _useCustomEmissions = widget.vehicleInfo.customEmissionsRate != null;
  }

  @override
  void dispose() {
    _fuelConsumptionController.dispose();
    _fuelPriceController.dispose();
    _electricityPriceController.dispose();
    _emissionsRateController.dispose();
    super.dispose();
  }

  void _updateVehicleInfo() {
    // Parse the fuel consumption value, defaulting to 8.0 if invalid
    double fuelConsumption = 8.0;
    try {
      fuelConsumption = double.parse(_fuelConsumptionController.text);
    } catch (e) {
      // Use default value if parsing fails
    }

    // Parse the fuel price value, defaulting to 1.50 if invalid
    double fuelPrice = 1.50;
    try {
      fuelPrice = double.parse(_fuelPriceController.text);
    } catch (e) {
      // Use default value if parsing fails
    }

    // Parse the electricity price value, defaulting to the default value if invalid
    double electricityPrice = VehicleInfo.defaultElectricityPrice;
    try {
      electricityPrice = double.parse(_electricityPriceController.text);
      // If the value is 0, use the default value
      if (electricityPrice <= 0) {
        electricityPrice = VehicleInfo.defaultElectricityPrice;
      }
    } catch (e) {
      // Use default value if parsing fails
    }

    // Parse the custom emissions rate if enabled
    double? customEmissionsRate;
    if (_useCustomEmissions) {
      try {
        customEmissionsRate = double.parse(_emissionsRateController.text);
      } catch (e) {
        // If invalid, fall back to the default value
        customEmissionsRate = null;
      }
    }

    // Create updated vehicle info
    final updatedVehicleInfo = VehicleInfo(
      engineType: _selectedEngineType,
      fuelConsumption: fuelConsumption,
      fuelPrice: fuelPrice,
      electricityPrice: electricityPrice,
      customEmissionsRate: customEmissionsRate,
    );

    // Notify parent
    widget.onVehicleInfoChanged(updatedVehicleInfo);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Column(
          children: [
            // Header with title and close button
            Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Vehicle Settings',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),

            // Scrollable content area
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Engine Type section
                      const Text(
                        'Engine Type',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: EngineType.values.length,
                          itemBuilder: (context, index) {
                            final engineType = EngineType.values[index];
                            final isSelected = engineType == _selectedEngineType;

                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedEngineType = engineType;
                                  // Update emissions rate when engine type changes
                                  if (!_useCustomEmissions) {
                                    _emissionsRateController.text =
                                        VehicleInfo.defaultEmissionsPerKm[engineType]!.toString();
                                  }
                                });
                                _updateVehicleInfo();
                              },
                              child: Container(
                                width: 80,
                                margin: const EdgeInsets.only(right: 8),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: isSelected ? engineType.color.withAlpha(51) : Colors.grey.shade200,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isSelected ? engineType.color : Colors.grey.shade300,
                                    width: 1,
                                  ),
                                  boxShadow: isSelected ? [
                                    BoxShadow(
                                      color: engineType.color.withAlpha(76),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    )
                                  ] : null,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      engineType.icon,
                                      color: isSelected ? engineType.color : Colors.grey.shade700,
                                      size: 32,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      engineType.displayName,
                                      style: TextStyle(
                                        color: isSelected ? engineType.color : Colors.grey.shade700,
                                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        fontSize: 12,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'Fuel Consumption (L/100km)',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _fuelConsumptionController,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Enter fuel consumption',
                          suffixText: 'L/100km',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        onChanged: (value) {
                          _updateVehicleInfo();
                        },
                      ),
                      const SizedBox(height: 16),
                      if (_selectedEngineType == EngineType.electric)
                        const Text(
                          'Note: For electric vehicles, this represents energy consumption in kWh/100km',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                        ),

                      const SizedBox(height: 24),
                      Text(
                        _selectedEngineType == EngineType.electric
                            ? 'Fuel Price (not applicable for electric vehicles)'
                            : 'Fuel Price (£ per litre)',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _selectedEngineType == EngineType.electric
                              ? Colors.grey
                              : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _fuelPriceController,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Enter fuel price',
                          prefixText: '£',
                          suffixText: 'per litre',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        onChanged: (value) {
                          _updateVehicleInfo();
                        },
                        enabled: _selectedEngineType != EngineType.electric,
                        style: TextStyle(
                          color: _selectedEngineType == EngineType.electric
                              ? Colors.grey
                              : Colors.black,
                        ),
                      ),

                      // Electricity Price Field (only for electric and hybrid vehicles)
                      if (_selectedEngineType == EngineType.electric || _selectedEngineType == EngineType.hybrid) ...[
                        const SizedBox(height: 24),
                        const Text(
                          'Electricity Price (£ per kWh)',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _electricityPriceController,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: 'Enter electricity price',
                            prefixText: '£',
                            suffixText: 'per kWh',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                          ],
                          onChanged: (value) {
                            _updateVehicleInfo();
                          },
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Default: £${VehicleInfo.defaultElectricityPrice} per kWh (if set to 0)',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],

                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Emissions Rate (kg CO₂e/km)',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Switch(
                            value: _useCustomEmissions,
                            activeColor: Colors.green,
                            onChanged: (value) {
                              setState(() {
                                _useCustomEmissions = value;
                                if (!value) {
                                  // Reset to default value when switching to automatic
                                  _emissionsRateController.text =
                                      VehicleInfo.defaultEmissionsPerKm[_selectedEngineType]!.toString();
                                }
                                _updateVehicleInfo();
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _emissionsRateController,
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(),
                          hintText: 'Enter emissions rate',
                          suffixText: 'kg CO₂e/km',
                          enabled: _useCustomEmissions,
                          fillColor: _useCustomEmissions ? null : Colors.grey.shade200,
                          filled: !_useCustomEmissions,
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,4}')),
                        ],
                        onChanged: (value) {
                          _updateVehicleInfo();
                        },
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _useCustomEmissions
                            ? 'Custom emissions value'
                            : 'Default emissions value for ${_selectedEngineType.displayName} engines',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          fontStyle: FontStyle.italic,
                        ),
                      ),


                    ],
                  ),
                ),
              ),
            ),

            // Fixed button at the bottom
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[800],
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text('Apply Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
