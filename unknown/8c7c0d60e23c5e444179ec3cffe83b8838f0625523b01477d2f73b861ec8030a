import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../services/routing_service.dart';
import '../services/places_service.dart';
import '../services/saved_routes_service.dart';
import '../widgets/bottom_sheets.dart';
import '../widgets/vehicle_settings_sidebar.dart';
import '../utils/map_utils.dart';
import '../models/vehicle_info.dart';
import '../models/saved_route.dart';
import '../screens/saved_routes_screen.dart';
import '../screens/journey_screen.dart';
import '../screens/journey_stats_screen.dart';

class MapScreen extends StatefulWidget {
  final Position initialPosition;

  const MapScreen({super.key, required this.initialPosition});

  @override
  MapScreenState createState() => MapScreenState();
}

class MapScreenState extends State<MapScreen> {
  late GoogleMapController mapController;
  bool _isBottomSheetVisible = false;
  final TextEditingController _startController = TextEditingController();
  final TextEditingController _destinationController = TextEditingController();
  Set<Polyline> _polylines = {};
  Set<Marker> _markers = {};

  // Route information
  List<RouteInfo> _routes = [];
  int _selectedRouteIndex = 0;
  LatLng? _startLocation;
  LatLng? _endLocation;
  bool _isRouteCalculated = false;

  // Vehicle information
  VehicleInfo _vehicleInfo = VehicleInfo();

  // Place selection

  void _toggleBottomSheet({bool? forceExpand}) {
    setState(() {
      if (forceExpand != null) {
        _isBottomSheetVisible = true;
        _isRouteCalculated = false;
      } else {
        _isBottomSheetVisible = !_isBottomSheetVisible;
      }
    });
  }

  void _toggleSidebar() {
    _showVehicleSettings();
  }

  // Show saved routes screen
  void _showSavedRoutes() {
    // Navigate directly to saved routes screen
    _navigateToSavedRoutes();
  }

  // Navigate to saved routes screen
  void _navigateToSavedRoutes() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SavedRoutesScreen(
          onRouteSelected: _loadSavedRoute,
        ),
      ),
    );
  }

  // Show journey statistics screen
  void _showJourneyStats() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const JourneyStatsScreen(),
      ),
    );
  }

  // Load a saved route
  void _loadSavedRoute(SavedRoute savedRoute) {
    try {
      // Set start and end addresses
      _startController.text = savedRoute.startAddress;
      _destinationController.text = savedRoute.endAddress;

      // Set locations
      _startLocation = savedRoute.startLocation;
      _endLocation = savedRoute.endLocation;

      // Create a route info object from the saved route
      final routeInfo = RouteInfo(
        points: savedRoute.encodedPolyline,
        distance: savedRoute.distance,
        duration: savedRoute.duration,
        summary: savedRoute.name,
        distanceValue: savedRoute.distanceValue,
        durationValue: savedRoute.durationValue,
        routeType: savedRoute.routeType,
      );

      // Update state
      setState(() {
        _routes = [routeInfo];
        _selectedRouteIndex = 0;
        _polylines = RoutingService.generatePolylines(_routes, _selectedRouteIndex);
        _isRouteCalculated = true;
      });

      // Move camera to show the route
      final selectedPoints = decodePolyline(_routes[_selectedRouteIndex].points);
      final bounds = boundsFromLatLngList(selectedPoints);
      mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));

      // Show bottom sheet
      _toggleBottomSheet();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Loaded route: ${savedRoute.name}')),
      );
    } catch (e) {
      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading route: ${e.toString()}')),
      );
    }
  }



  void _showVehicleSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: VehicleSettingsSidebar(
          vehicleInfo: _vehicleInfo,
          onVehicleInfoChanged: (updatedVehicleInfo) {
            setState(() {
              _vehicleInfo = updatedVehicleInfo;
            });

            // Recalculate routes with new vehicle info if routes exist
            if (_isRouteCalculated && _startLocation != null && _endLocation != null) {
              _calculateRoute();
            }
          },
        ),
      ),
    );
  }

  // Handle map tap to add a marker
  void _handleMapTap(LatLng latLng) async {
    if (_isBottomSheetVisible && _isRouteCalculated) {
      _toggleBottomSheet();
      return;
    }

    // Get address for the tapped location
    final address = await PlacesService.getAddressFromLatLng(latLng);
    if (address == null) return;

    // Create a marker for the tapped location
    final marker = PlacesService.createMarker(
      id: 'tapped_location',
      position: latLng,
      title: 'Selected Location',
      snippet: address,
    );

    setState(() {
      _markers = {marker};
    });

    // Show dialog to let user choose if this is start or destination
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Use this location'),
        content: Text('Address: $address'),
        actions: [
          TextButton(
            onPressed: () {
              _startController.text = address;
              Navigator.pop(context);
            },
            child: const Text('Set as Start'),
          ),
          TextButton(
            onPressed: () {
              _destinationController.text = address;
              Navigator.pop(context);
            },
            child: const Text('Set as Destination'),
          ),
        ],
      ),
    );
  }

  // Handle place selection from autocomplete
  void _handleStartPlaceSelected(String placeId, String description) {
    _getPlaceDetails(placeId, isStart: true);
  }

  void _handleDestinationPlaceSelected(String placeId, String description) {
    _getPlaceDetails(placeId, isStart: false);
  }

  // Get place details and add marker
  Future<void> _getPlaceDetails(String placeId, {required bool isStart}) async {
    final placeData = await PlacesService.getPlaceDetails(placeId);
    if (placeData == null || placeData.location == null) return;

    final location = placeData.location!;

    // Create marker
    final marker = PlacesService.createMarker(
      id: isStart ? 'start_location' : 'destination_location',
      position: location,
      title: isStart ? 'Start' : 'Destination',
      snippet: placeData.address,
      icon: BitmapDescriptor.defaultMarkerWithHue(
        isStart ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueRed,
      ),
    );

    setState(() {
      if (isStart) {
        _startLocation = location;
      } else {
        _endLocation = location;
      }

      // Update markers
      _markers = {..._markers.where((m) => m.markerId.value != marker.markerId.value), marker};
    });

    // Move camera to the selected location
    mapController.animateCamera(CameraUpdate.newLatLngZoom(location, 15));
  }

  Future<void> _calculateRoute() async {
    if (_startController.text.isEmpty || _destinationController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter both addresses')),
      );
      return;
    }

    try {

      // Get coordinates for start location
      final startLocation = await RoutingService.getCoordinatesForAddress(_startController.text);
      if (startLocation == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not find start location')),
        );
        return;
      }
      _startLocation = startLocation;

      // Get coordinates for end location
      final endLocation = await RoutingService.getCoordinatesForAddress(_destinationController.text);
      if (endLocation == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not find destination')),
        );
        return;
      }
      _endLocation = endLocation;

      // Calculate routes with vehicle info for eco-friendly metrics
      final routes = await RoutingService.calculateRoutes(
        _startLocation!,
        _endLocation!,
        vehicleInfo: _vehicleInfo,
      );

      if (routes.isEmpty) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No routes found')),
        );
        return;
      }

      // Update state with the first route selected by default
      setState(() {
        _selectedRouteIndex = 0;
        _routes = routes;
        _polylines = RoutingService.generatePolylines(routes, _selectedRouteIndex);
        _isRouteCalculated = true;
      });

      _toggleBottomSheet();

      // Move camera to show the entire route
      final selectedPoints = decodePolyline(_routes[_selectedRouteIndex].points);
      final bounds = boundsFromLatLngList(selectedPoints);
      mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
    } catch (e) {
      // Show error message to user
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
    }
  }

  // Method to select a different route
  void _selectRoute(int index) {
    if (index < 0 || index >= _routes.length) return;

    setState(() {
      _selectedRouteIndex = index;
      _polylines = RoutingService.generatePolylines(_routes, _selectedRouteIndex);
    });

    // Move camera to show the selected route
    final selectedPoints = decodePolyline(_routes[_selectedRouteIndex].points);
    final bounds = boundsFromLatLngList(selectedPoints);
    mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
  }

  // Use current location as starting point
  Future<void> _useCurrentLocationAsStart() async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Getting your current location...')),
      );

      // Request location permission if not granted
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requestedPermission = await Geolocator.requestPermission();
        if (requestedPermission == LocationPermission.denied) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permission denied')),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location permission permanently denied. Please enable it in settings.'),
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address for the current location
      final currentLocation = LatLng(position.latitude, position.longitude);
      final address = await PlacesService.getAddressFromLatLng(currentLocation);

      if (address == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not determine your address')),
        );
        return;
      }

      // Create a marker for the current location
      final marker = PlacesService.createMarker(
        id: 'start_location',
        position: currentLocation,
        title: 'Current Location',
        snippet: address,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      );

      // Update state
      setState(() {
        _startController.text = address;
        _startLocation = currentLocation;
        _markers = {..._markers.where((m) => m.markerId.value != marker.markerId.value), marker};
      });

      // Move camera to the current location
      mapController.animateCamera(CameraUpdate.newLatLngZoom(currentLocation, 15));

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Using current location: $address')),
      );
    } catch (e) {
      // Show error message to user
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: ${e.toString()}')),
      );
    }
  }

  // Save the current route
  void _saveCurrentRoute() {
    if (_routes.isEmpty || _selectedRouteIndex >= _routes.length) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No route to save')),
      );
      return;
    }

    // Show dialog to name the route
    _showSaveRouteDialog();
  }

  // Start navigation for the selected route
  void _startNavigation() {
    if (_routes.isEmpty || _selectedRouteIndex >= _routes.length) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No route available for navigation')),
      );
      return;
    }

    if (_startLocation == null || _endLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Start or destination location is missing')),
      );
      return;
    }

    // Find the fastest route for comparison
    RouteInfo? fastestRoute;
    for (final route in _routes) {
      if (route.routeType == RouteType.fastest || route.routeType == RouteType.fastestAndEco) {
        fastestRoute = route;
        break;
      }
    }

    // If no fastest route is found, use the first route as a fallback
    if (fastestRoute == null && _routes.isNotEmpty) {
      fastestRoute = _routes[0];
    }

    // Navigate to the journey screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JourneyScreen(
          route: _routes[_selectedRouteIndex],
          startAddress: _startController.text,
          endAddress: _destinationController.text,
          startLocation: _startLocation!,
          endLocation: _endLocation!,
          fastestRoute: fastestRoute,
        ),
      ),
    );
  }

  // Show dialog to name the route before saving
  void _showSaveRouteDialog() {
    final TextEditingController nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Route'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Give this route a name:'),
            const SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Route Name',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final routeName = nameController.text.trim();
              if (routeName.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a name for the route')),
                );
                return;
              }

              Navigator.pop(context);
              _saveRouteToDatabase(routeName);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }



  // Save route to database
  Future<void> _saveRouteToDatabase(String routeName) async {
    if (_routes.isEmpty || _selectedRouteIndex >= _routes.length) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No route to save')),
      );
      return;
    }

    try {
      // Get the current route
      final routeInfo = _routes[_selectedRouteIndex];

      // Save locally
      final success = await SavedRoutesService.saveRouteLocally(
        name: routeName,
        startAddress: _startController.text,
        endAddress: _destinationController.text,
        startLocation: _startLocation!,
        endLocation: _endLocation!,
        routeInfo: routeInfo,
      );

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Route "$routeName" saved successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save route')),
        );
      }
    } catch (e) {
      // Show error message to user
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving route: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: (controller) {
              setState(() {
                mapController = controller;
              });
            },
            initialCameraPosition: CameraPosition(
              target: LatLng(
                widget.initialPosition.latitude,
                widget.initialPosition.longitude,
              ),
              zoom: 15.0,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            polylines: _polylines,
            markers: _markers,
            onTap: _handleMapTap,
          ),
          if (_isBottomSheetVisible)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                constraints: BoxConstraints(
                  maxHeight: _isRouteCalculated
                      ? (_routes.length > 1 ? 480.0 : 420.0) // Increased height for navigation button
                      : MediaQuery.of(context).size.height * 0.8, // Max height for expanded state
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(51), // 20% opacity
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: _isRouteCalculated
                    ? CollapsedBottomSheet(
                        routes: _routes,
                        selectedRouteIndex: _selectedRouteIndex,
                        onRouteSelected: _selectRoute,
                        onChangeRoute: () => _toggleBottomSheet(forceExpand: true),
                        onSaveRoute: _saveCurrentRoute,
                        onStartNavigation: _startNavigation,
                      )
                    : ExpandedBottomSheet(
                        startController: _startController,
                        destinationController: _destinationController,
                        onCalculateRoute: _calculateRoute,
                        onStartPlaceSelected: _handleStartPlaceSelected,
                        onDestinationPlaceSelected: _handleDestinationPlaceSelected,
                        onUseCurrentLocation: _useCurrentLocationAsStart,
                      ),
              ),
            ),

        ],
      ),
      floatingActionButton: _isBottomSheetVisible ? null : FloatingActionButton.extended(
        onPressed: _toggleBottomSheet,
        backgroundColor: Colors.green[800],
        label: const Text(
          'Plan Journey',
          style: TextStyle(color: Colors.white),
        ),
        icon: const Icon(Icons.directions, color: Colors.white),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'EcoRoute',
          style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
        ),
        actions: [
          // Journey Stats Button
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: const Icon(
                Icons.analytics,
                color: Colors.orange,
              ),
              tooltip: 'Journey Statistics',
              onPressed: _showJourneyStats,
            ),
          ),
          // Saved Routes Button
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: const Icon(
                Icons.bookmark,
                color: Colors.blue,
              ),
              tooltip: 'Saved Routes',
              onPressed: _showSavedRoutes,
            ),
          ),
          // Vehicle Settings Button
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: IconButton(
              icon: Icon(
                _vehicleInfo.engineType.icon,
                color: _vehicleInfo.engineType.color,
              ),
              tooltip: 'Vehicle Settings',
              onPressed: _toggleSidebar,
            ),
          ),
        ],
      ),
    );
  }
}
