import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import '../utils/map_utils.dart';
import '../models/vehicle_info.dart';
import 'elevation_service.dart';

/// Enum representing traffic speed categories from the Google Routes API
enum TrafficSpeed {
  normal,
  slow,
  trafficJam,
  unknown
}

/// Extension to provide helper methods for TrafficSpeed
extension TrafficSpeedExtension on TrafficSpeed {
  /// Get a congestion factor based on the traffic speed
  /// Higher values indicate more congestion and thus higher fuel consumption
  double get congestionFactor {
    switch (this) {
      case TrafficSpeed.normal:
        return 1.0; // No additional consumption
      case TrafficSpeed.slow:
        return 1.3; // 30% more fuel consumption in slow traffic
      case TrafficSpeed.trafficJam:
        return 1.6; // 60% more fuel consumption in traffic jams
      case TrafficSpeed.unknown:
        return 1.0; // Default to no effect
    }
  }

  /// Create a TrafficSpeed from a string value returned by the API
  static TrafficSpeed fromString(String? value) {
    if (value == null) return TrafficSpeed.unknown;

    switch (value.toUpperCase()) {
      case 'NORMAL':
        return TrafficSpeed.normal;
      case 'SLOW':
        return TrafficSpeed.slow;
      case 'TRAFFIC_JAM':
        return TrafficSpeed.trafficJam;
      default:
        return TrafficSpeed.unknown;
    }
  }
}

enum RouteType {
  regular,
  fastest,
  ecoFriendly,
  fastestAndEco
}

extension RouteTypeExtension on RouteType {
  String get displayName {
    switch (this) {
      case RouteType.regular:
        return 'Route';
      case RouteType.fastest:
        return 'Fastest Route';
      case RouteType.ecoFriendly:
        return 'Eco Route';
      case RouteType.fastestAndEco:
        return 'Fastest + Eco Route';
    }
  }

  Color get color {
    switch (this) {
      case RouteType.regular:
        return Colors.blue;
      case RouteType.fastest:
        return Colors.orange;
      case RouteType.ecoFriendly:
        return Colors.green;
      case RouteType.fastestAndEco:
        return Colors.teal;
    }
  }
}

class RouteInfo {
  final String points;
  final String distance;
  final String duration;
  final String summary;
  final double distanceValue; // Distance in kilometers
  final int durationValue; // Duration in seconds
  final double emissions; // CO2 emissions in kg
  final double fuelConsumption; // Fuel consumption in litres
  final double fuelCost; // Fuel cost in currency units
  final double gradientFactor; // Factor to adjust fuel consumption based on route gradient
  final double trafficFactor; // Factor to adjust fuel consumption based on traffic conditions
  RouteType routeType; // Type of route (fastest, eco-friendly, etc.)
  final Map<TrafficSpeed, double>? trafficBreakdown; // Percentage of route in each traffic condition

  RouteInfo({
    required this.points,
    required this.distance,
    required this.duration,
    required this.summary,
    required this.distanceValue,
    required this.durationValue,
    this.emissions = 0.0,
    this.fuelConsumption = 0.0,
    this.fuelCost = 0.0,
    this.gradientFactor = 1.0, // Default to no effect (1.0)
    this.trafficFactor = 1.0, // Default to no effect (1.0)
    this.routeType = RouteType.regular,
    this.trafficBreakdown,
  });
}

class RoutingService {
  static const String _apiKey = 'AIzaSyCW81h6F10_4BmFbjlvHdLgxv8xX3hrspw';

  /// Get coordinates for an address using Google Geocoding API
  static Future<LatLng?> getCoordinatesForAddress(String address) async {
    final url = Uri.parse(
      'https://maps.googleapis.com/maps/api/geocode/json?address=$address&key=$_apiKey',
    );

    try {
      final response = await http.get(url);
      final json = jsonDecode(response.body);

      if (json['status'] != 'OK') {
        throw Exception('Geocoding error: ${json['status']}');
      }

      final location = json['results'][0]['geometry']['location'];
      return LatLng(location['lat'], location['lng']);
    } catch (e) {
      // Error handled by returning null
      return null;
    }
  }

  /// Helper method to check if two routes are similar based on their encoded polylines
  static bool areRoutesSimilar(String polyline1, String polyline2, {double similarityThreshold = 0.8}) {
    // Decode both polylines
    List<LatLng> points1 = decodePolyline(polyline1);
    List<LatLng> points2 = decodePolyline(polyline2);

    // If the routes have very different lengths, they're probably different routes
    if (points1.length < points2.length * 0.5 || points1.length > points2.length * 1.5) {
      return false;
    }

    // Sample points from both routes for comparison
    // Use fewer points for efficiency
    int sampleSize = 10;
    List<LatLng> sampledPoints1 = _samplePoints(points1, sampleSize);
    List<LatLng> sampledPoints2 = _samplePoints(points2, sampleSize);

    // Count how many points are close to each other
    int matchingPoints = 0;
    for (int i = 0; i < sampledPoints1.length; i++) {
      for (int j = 0; j < sampledPoints2.length; j++) {
        double distance = _calculateDistance(sampledPoints1[i], sampledPoints2[j]);
        if (distance < 0.01) { // 0.01 degrees is roughly 1km
          matchingPoints++;
          break;
        }
      }
    }

    // Calculate similarity ratio
    double similarity = matchingPoints / sampleSize;
    return similarity >= similarityThreshold;
  }

  /// Sample points from a route
  static List<LatLng> _samplePoints(List<LatLng> points, int sampleSize) {
    if (points.length <= sampleSize) return List.from(points);

    List<LatLng> sampledPoints = [];
    double step = points.length / sampleSize;

    for (int i = 0; i < sampleSize; i++) {
      int index = (i * step).floor();
      if (index >= points.length) index = points.length - 1;
      sampledPoints.add(points[index]);
    }

    return sampledPoints;
  }

  /// Calculate distance between two LatLng points
  static double _calculateDistance(LatLng point1, LatLng point2) {
    return (point1.latitude - point2.latitude).abs() + (point1.longitude - point2.longitude).abs();
  }

  /// Calculate traffic factor from speed reading intervals
  /// Returns a factor to adjust fuel consumption and emissions based on traffic conditions
  /// Also returns a breakdown of the route by traffic condition
  static Map<String, dynamic> _calculateTrafficFactor(List<dynamic>? speedReadingIntervals, int totalPoints) {
    // Default to no traffic effect if no data is available
    if (speedReadingIntervals == null || speedReadingIntervals.isEmpty || totalPoints <= 0) {
      return {
        'trafficFactor': 1.0,
        'breakdown': null
      };
    }

    // Initialize counters for each traffic condition
    int normalPoints = 0;
    int slowPoints = 0;
    int jamPoints = 0;

    // Process each speed reading interval
    for (var interval in speedReadingIntervals) {
      // Get the start and end indices
      int startIndex = interval['startPolylinePointIndex'] ?? 0;
      int endIndex = interval['endPolylinePointIndex'] ?? 0;

      // Calculate the number of points in this interval
      int intervalPoints = endIndex - startIndex;

      // Skip invalid intervals
      if (intervalPoints <= 0) continue;

      // Get the traffic speed for this interval
      TrafficSpeed speed = TrafficSpeedExtension.fromString(interval['speed']);

      // Update the counters based on the traffic condition
      switch (speed) {
        case TrafficSpeed.normal:
          normalPoints += intervalPoints;
          break;
        case TrafficSpeed.slow:
          slowPoints += intervalPoints;
          break;
        case TrafficSpeed.trafficJam:
          jamPoints += intervalPoints;
          break;
        case TrafficSpeed.unknown:
          // Count unknown as normal to be conservative
          normalPoints += intervalPoints;
          break;
      }
    }

    // Calculate the percentage of the route in each traffic condition
    double normalPercent = normalPoints / totalPoints;
    double slowPercent = slowPoints / totalPoints;
    double jamPercent = jamPoints / totalPoints;

    // Create a breakdown of the route by traffic condition
    Map<TrafficSpeed, double> breakdown = {
      TrafficSpeed.normal: normalPercent,
      TrafficSpeed.slow: slowPercent,
      TrafficSpeed.trafficJam: jamPercent,
    };

    // Calculate the weighted traffic factor
    double trafficFactor = (normalPercent * TrafficSpeed.normal.congestionFactor) +
                          (slowPercent * TrafficSpeed.slow.congestionFactor) +
                          (jamPercent * TrafficSpeed.trafficJam.congestionFactor);

    // Ensure the factor is within reasonable bounds
    trafficFactor = trafficFactor.clamp(1.0, 2.0);



    return {
      'trafficFactor': trafficFactor,
      'breakdown': breakdown
    };
  }

  /// Calculate routes between two points using Google Routes API
  static Future<List<RouteInfo>> calculateRoutes(LatLng start, LatLng end, {VehicleInfo? vehicleInfo}) async {
    final routesUrl = Uri.parse('https://routes.googleapis.com/directions/v2:computeRoutes');

    // Create base request body for Routes API
    final baseRequestBody = {
      "origin": {
        "location": {
          "latLng": {
            "latitude": start.latitude,
            "longitude": start.longitude
          }
        }
      },
      "destination": {
        "location": {
          "latLng": {
            "latitude": end.latitude,
            "longitude": end.longitude
          }
        }
      },
      "travelMode": "DRIVE",
      "routingPreference": "TRAFFIC_AWARE",
      "computeAlternativeRoutes": true,
      "extraComputations": ["TRAFFIC_ON_POLYLINE"], // Request traffic data
      "languageCode": "en-US",
      "units": "METRIC"
    };

    // First request: Default routes
    final defaultRequestBody = Map<String, dynamic>.from(baseRequestBody);
    defaultRequestBody["routeModifiers"] = {
      "avoidTolls": false,
      "avoidHighways": false,
      "avoidFerries": false
    };

    // Second request: Avoid highways
    final avoidHighwaysRequestBody = Map<String, dynamic>.from(baseRequestBody);
    avoidHighwaysRequestBody["routeModifiers"] = {
      "avoidTolls": false,
      "avoidHighways": true,
      "avoidFerries": false
    };

    try {
      // Make first POST request to Routes API (default routes)
      final defaultRoutesResponse = await http.post(
        routesUrl,
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': _apiKey,
          'X-Goog-FieldMask': 'routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline,routes.description,routes.travelAdvisory.speedReadingIntervals'
        },
        body: jsonEncode(defaultRequestBody),
      );

      final defaultRoutesJson = jsonDecode(defaultRoutesResponse.body);

      if (defaultRoutesResponse.statusCode != 200) {
        throw Exception('Routes API error: ${defaultRoutesJson['error']['message'] ?? "Unknown error"}');
      }

      // Make second POST request to Routes API (avoid highways)
      final avoidHighwaysResponse = await http.post(
        routesUrl,
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': _apiKey,
          'X-Goog-FieldMask': 'routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline,routes.description,routes.travelAdvisory.speedReadingIntervals'
        },
        body: jsonEncode(avoidHighwaysRequestBody),
      );

      final avoidHighwaysJson = jsonDecode(avoidHighwaysResponse.body);

      // If the second request fails, we'll just use the results from the first request
      List<dynamic> combinedRoutesData = [];

      // Add routes from the first request
      if (defaultRoutesJson.containsKey('routes')) {
        combinedRoutesData.addAll(defaultRoutesJson['routes']);
      }

      // Add routes from the second request if successful
      if (avoidHighwaysResponse.statusCode == 200 && avoidHighwaysJson.containsKey('routes')) {
        // For each route in the second request, check if it's similar to any route we already have
        for (var newRoute in avoidHighwaysJson['routes']) {
          bool isDuplicate = false;
          String newPolyline = newRoute['polyline']['encodedPolyline'];

          for (var existingRoute in combinedRoutesData) {
            String existingPolyline = existingRoute['polyline']['encodedPolyline'];
            if (areRoutesSimilar(existingPolyline, newPolyline)) {
              isDuplicate = true;
              break;
            }
          }

          if (!isDuplicate) {
            // Add a note to the description that this is a non-highway route
            if (newRoute['description'] != null) {
              newRoute['description'] = "${newRoute['description']} (No Highways)";
            } else {
              newRoute['description'] = "Route (No Highways)";
            }
            combinedRoutesData.add(newRoute);
          }
        }
      }



      List<RouteInfo> routes = [];

      // Process all routes from combined data
      for (int i = 0; i < combinedRoutesData.length; i++) {
        final route = combinedRoutesData[i];
        final points = route['polyline']['encodedPolyline'];

        // Convert distance from meters to kilometers with one decimal place
        final distanceMeters = route['distanceMeters'];
        final distanceKmValue = distanceMeters / 1000;
        final distanceKm = distanceKmValue.toStringAsFixed(1);
        final distance = '$distanceKm km';

        // Convert duration from seconds to hours, minutes, and seconds
        final durationString = route['duration'];

        // Extract the numeric value (assuming format like "761s" or "12m3s")
        int totalSeconds = 0;

        // Parse hours if present
        RegExp hourRegex = RegExp(r'(\d+)h');
        Match? hourMatch = hourRegex.firstMatch(durationString);
        if (hourMatch != null) {
          totalSeconds += int.parse(hourMatch.group(1)!) * 3600;
        }

        // Parse minutes if present
        RegExp minuteRegex = RegExp(r'(\d+)m');
        Match? minuteMatch = minuteRegex.firstMatch(durationString);
        if (minuteMatch != null) {
          totalSeconds += int.parse(minuteMatch.group(1)!) * 60;
        }

        // Parse seconds if present
        RegExp secondRegex = RegExp(r'(\d+)s');
        Match? secondMatch = secondRegex.firstMatch(durationString);
        if (secondMatch != null) {
          totalSeconds += int.parse(secondMatch.group(1)!);
        }

        // Format the duration nicely
        String duration = '';
        int hours = totalSeconds ~/ 3600;
        int minutes = (totalSeconds % 3600) ~/ 60;
        int seconds = totalSeconds % 60;

        if (hours > 0) {
          duration += '$hours hr ';
        }
        if (minutes > 0 || hours > 0) {
          duration += '$minutes min ';
        }
        duration += '$seconds sec';

        // Get route description or use a default
        final summary = route['description'] ?? 'Route ${i + 1}';

        // Calculate eco-friendly metrics if vehicle info is provided
        double emissions = 0.0;
        double fuelConsumption = 0.0;
        double fuelCost = 0.0;
        double gradientFactor = 1.0; // Default to no effect
        double trafficFactor = 1.0; // Default to no effect
        Map<TrafficSpeed, double>? trafficBreakdown;

        // Get route points for elevation calculation
        List<LatLng> routePoints = decodePolyline(points);

        // Get elevations for the route points
        List<double> elevations = await ElevationService.getElevationsForRoute(routePoints);

        // Calculate gradient factor if we have elevation data
        if (elevations.isNotEmpty) {
          gradientFactor = ElevationService.calculateGradientFactor(routePoints, elevations);

        }

        // Calculate traffic factor if traffic data is available
        if (route.containsKey('travelAdvisory') &&
            route['travelAdvisory'] != null &&
            route['travelAdvisory'].containsKey('speedReadingIntervals')) {

          final speedReadingIntervals = route['travelAdvisory']['speedReadingIntervals'];
          final totalPoints = routePoints.length;

          // Calculate traffic factor and breakdown
          final trafficData = _calculateTrafficFactor(speedReadingIntervals, totalPoints);
          trafficFactor = trafficData['trafficFactor'];
          trafficBreakdown = trafficData['breakdown'];


        }

        if (vehicleInfo != null) {
          // Calculate emissions based on vehicle type, distance, gradient, and traffic
          emissions = vehicleInfo.calculateEmissions(distanceKmValue, gradientFactor, trafficFactor);

          // Calculate fuel consumption with gradient and traffic factors
          fuelConsumption = vehicleInfo.engineType == EngineType.electric
              ? 0.0 // Electric vehicles don't consume fuel
              : (vehicleInfo.fuelConsumption * distanceKmValue * gradientFactor * trafficFactor / 100);

          // Calculate fuel cost using the fuel price from vehicle info, including traffic factor
          fuelCost = vehicleInfo.calculateFuelCost(distanceKmValue, gradientFactor, trafficFactor);
        }

        routes.add(RouteInfo(
          points: points,
          distance: distance,
          duration: duration,
          summary: summary,
          distanceValue: distanceKmValue,
          durationValue: totalSeconds,
          emissions: emissions,
          fuelConsumption: fuelConsumption,
          fuelCost: fuelCost,
          gradientFactor: gradientFactor,
          trafficFactor: trafficFactor,
          trafficBreakdown: trafficBreakdown,
        ));
      }

      // Identify the fastest and most eco-friendly routes
      if (routes.length > 1 && vehicleInfo != null) {
        // Find the fastest route (lowest duration)
        int fastestIndex = 0;
        int minDuration = routes[0].durationValue;

        for (int i = 1; i < routes.length; i++) {
          if (routes[i].durationValue < minDuration) {
            minDuration = routes[i].durationValue;
            fastestIndex = i;
          }
        }

        // Find the most eco-friendly route (lowest emissions)
        int ecoIndex = 0;
        double minEmissions = routes[0].emissions;

        for (int i = 1; i < routes.length; i++) {
          if (routes[i].emissions < minEmissions) {
            minEmissions = routes[i].emissions;
            ecoIndex = i;
          }
        }

        // Update route types
        for (int i = 0; i < routes.length; i++) {
          if (i == fastestIndex && i == ecoIndex) {
            // This route is both fastest and most eco-friendly
            routes[i].routeType = RouteType.fastestAndEco;
          } else if (i == fastestIndex) {
            // This route is the fastest
            routes[i].routeType = RouteType.fastest;
          } else if (i == ecoIndex) {
            // This route is the most eco-friendly
            routes[i].routeType = RouteType.ecoFriendly;
          } else {
            // This is a regular route
            routes[i].routeType = RouteType.regular;
          }
        }
      }

      return routes;
    } catch (e) {
      debugPrint('Error calculating routes: $e');
      return [];
    }
  }

  /// Generate polylines for routes with appropriate colors
  static Set<Polyline> generatePolylines(List<RouteInfo> routes, int selectedIndex) {
    Set<Polyline> polylines = {};

    for (int i = 0; i < routes.length; i++) {
      // Use the route type color
      Color routeColor = routes[i].routeType.color;

      polylines.add(
        Polyline(
          polylineId: PolylineId('route_$i'),
          points: decodePolyline(routes[i].points),
          color: routeColor,
          width: i == selectedIndex ? 6 : 4, // Make selected route thicker
        ),
      );
    }

    return polylines;
  }
}
