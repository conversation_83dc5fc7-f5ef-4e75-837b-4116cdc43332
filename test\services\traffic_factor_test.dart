import 'package:flutter_test/flutter_test.dart';
import 'package:eco_route/services/routing_service.dart';

// Create a test wrapper for the private _calculateTrafficFactor method
class TrafficFactorTestWrapper {
  static Map<String, dynamic> calculateTrafficFactor(List<dynamic>? speedReadingIntervals, int totalPoints) {
    // This is a copy of the private method for testing purposes
    // Default to no traffic effect if no data is available
    if (speedReadingIntervals == null || speedReadingIntervals.isEmpty || totalPoints <= 0) {
      return {
        'trafficFactor': 1.0,
        'breakdown': null
      };
    }

    // Initialize counters for each traffic condition
    int normalPoints = 0;
    int slowPoints = 0;
    int jamPoints = 0;

    // Process each speed reading interval
    for (var interval in speedReadingIntervals) {
      // Get the start and end indices
      int startIndex = interval['startPolylinePointIndex'] ?? 0;
      int endIndex = interval['endPolylinePointIndex'] ?? 0;

      // Calculate the number of points in this interval
      int intervalPoints = endIndex - startIndex;

      // Skip invalid intervals
      if (intervalPoints <= 0) continue;

      // Get the traffic speed for this interval
      TrafficSpeed speed = TrafficSpeedExtension.fromString(interval['speed']);

      // Update the counters based on the traffic condition
      switch (speed) {
        case TrafficSpeed.normal:
          normalPoints += intervalPoints;
          break;
        case TrafficSpeed.slow:
          slowPoints += intervalPoints;
          break;
        case TrafficSpeed.trafficJam:
          jamPoints += intervalPoints;
          break;
        case TrafficSpeed.unknown:
          // Count unknown as normal to be conservative
          normalPoints += intervalPoints;
          break;
      }
    }

    // Calculate the percentage of the route in each traffic condition
    double normalPercent = normalPoints / totalPoints;
    double slowPercent = slowPoints / totalPoints;
    double jamPercent = jamPoints / totalPoints;

    // Create a breakdown of the route by traffic condition
    Map<TrafficSpeed, double> breakdown = {
      TrafficSpeed.normal: normalPercent,
      TrafficSpeed.slow: slowPercent,
      TrafficSpeed.trafficJam: jamPercent,
    };

    // Calculate the weighted traffic factor
    double trafficFactor = (normalPercent * TrafficSpeed.normal.congestionFactor) +
                          (slowPercent * TrafficSpeed.slow.congestionFactor) +
                          (jamPercent * TrafficSpeed.trafficJam.congestionFactor);

    // Ensure the factor is within reasonable bounds
    trafficFactor = trafficFactor.clamp(1.0, 2.0);

    return {
      'trafficFactor': trafficFactor,
      'breakdown': breakdown
    };
  }
}

void main() {
  group('Traffic Factor Calculation', () {
    test('returns default values when no speed readings are provided', () {
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(null, 100);
      
      expect(result['trafficFactor'], equals(1.0));
      expect(result['breakdown'], isNull);
    });
    
    test('returns default values when speed readings list is empty', () {
      final result = TrafficFactorTestWrapper.calculateTrafficFactor([], 100);
      
      expect(result['trafficFactor'], equals(1.0));
      expect(result['breakdown'], isNull);
    });
    
    test('returns default values when total points is zero or negative', () {
      final speedReadings = [
        {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 50, 'speed': 'NORMAL'}
      ];
      
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 0);
      
      expect(result['trafficFactor'], equals(1.0));
      expect(result['breakdown'], isNull);
    });
    
    test('calculates traffic factor correctly for all normal traffic', () {
      final speedReadings = [
        {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 100, 'speed': 'NORMAL'}
      ];
      
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 100);
      
      expect(result['trafficFactor'], closeTo(1.0, 0.01)); // Normal traffic has factor 1.0
      
      final breakdown = result['breakdown'] as Map<TrafficSpeed, double>;
      expect(breakdown[TrafficSpeed.normal], closeTo(1.0, 0.01));
      expect(breakdown[TrafficSpeed.slow], closeTo(0.0, 0.01));
      expect(breakdown[TrafficSpeed.trafficJam], closeTo(0.0, 0.01));
    });
    
    test('calculates traffic factor correctly for mixed traffic conditions', () {
      final speedReadings = [
        {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 50, 'speed': 'NORMAL'},
        {'startPolylinePointIndex': 50, 'endPolylinePointIndex': 75, 'speed': 'SLOW'},
        {'startPolylinePointIndex': 75, 'endPolylinePointIndex': 100, 'speed': 'TRAFFIC_JAM'}
      ];
      
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 100);
      
      // Expected factor: (50% × 1.0) + (25% × 1.3) + (25% × 1.6) = 1.225
      expect(result['trafficFactor'], closeTo(1.225, 0.01));
      
      final breakdown = result['breakdown'] as Map<TrafficSpeed, double>;
      expect(breakdown[TrafficSpeed.normal], closeTo(0.5, 0.01));
      expect(breakdown[TrafficSpeed.slow], closeTo(0.25, 0.01));
      expect(breakdown[TrafficSpeed.trafficJam], closeTo(0.25, 0.01));
    });
    
    test('handles unknown traffic conditions by treating them as normal', () {
      final speedReadings = [
        {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 50, 'speed': 'NORMAL'},
        {'startPolylinePointIndex': 50, 'endPolylinePointIndex': 100, 'speed': 'UNKNOWN'}
      ];
      
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 100);
      
      // Both segments should be treated as normal traffic with factor 1.0
      expect(result['trafficFactor'], closeTo(1.0, 0.01));
      
      final breakdown = result['breakdown'] as Map<TrafficSpeed, double>;
      expect(breakdown[TrafficSpeed.normal], closeTo(1.0, 0.01));
    });
    
    test('clamps traffic factor to maximum of 2.0', () {
      // Create a scenario with all traffic jams, which would normally give a factor of 1.6
      // but we'll use a very high congestion factor to test clamping
      final speedReadings = [
        {'startPolylinePointIndex': 0, 'endPolylinePointIndex': 100, 'speed': 'TRAFFIC_JAM'}
      ];
      
      final result = TrafficFactorTestWrapper.calculateTrafficFactor(speedReadings, 100);
      
      // Factor should be clamped to 2.0 maximum
      expect(result['trafficFactor'], lessThanOrEqualTo(2.0));
    });
  });
}
