import 'package:flutter/material.dart';
import '../services/places_service.dart';

class PlaceAutocomplete extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final Function(String placeId, String description) onPlaceSelected;
  final bool readOnly;
  final bool showLocationButton;
  final VoidCallback? onUseCurrentLocation;

  const PlaceAutocomplete({
    super.key,
    required this.controller,
    required this.label,
    required this.onPlaceSelected,
    this.readOnly = false,
    this.showLocationButton = false,
    this.onUseCurrentLocation,
  });

  @override
  State<PlaceAutocomplete> createState() => _PlaceAutocompleteState();
}

class _PlaceAutocompleteState extends State<PlaceAutocomplete> {
  List<Map<String, String>> _predictions = [];
  bool _isLoading = false;
  final FocusNode _focusNode = FocusNode();
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onSearchChanged);
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _showOverlay();
      } else {
        _hideOverlay();
      }
    });
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onSearchChanged);
    _focusNode.dispose();
    _hideOverlay();
    super.dispose();
  }

  Future<void> _onSearchChanged() async {
    if (!_focusNode.hasFocus) return;

    final input = widget.controller.text;
    if (input.isEmpty) {
      setState(() {
        _predictions = [];
      });
      _updateOverlay();
      return;
    }

    setState(() {
      _isLoading = true;
    });
    _updateOverlay();

    try {
      final predictions = await PlacesService.getPlacePredictions(input);
      setState(() {
        _predictions = predictions;
        _isLoading = false;
      });
      _updateOverlay();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _updateOverlay();
    }
  }

  void _showOverlay() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    _hideOverlay();
    if (_focusNode.hasFocus) {
      _showOverlay();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 4,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 300,
                minWidth: size.width,
              ),
              child: _isLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : _predictions.isEmpty
                      ? const ListTile(
                          title: Text('No results found'),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: _predictions.length,
                          itemBuilder: (context, index) {
                            final prediction = _predictions[index];
                            return ListTile(
                              title: Text(prediction['description'] ?? ''),
                              onTap: () {
                                widget.controller.text = prediction['description'] ?? '';
                                widget.onPlaceSelected(
                                  prediction['placeId'] ?? '',
                                  prediction['description'] ?? '',
                                );
                                _hideOverlay();
                                _focusNode.unfocus();
                              },
                            );
                          },
                        ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: widget.controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          labelText: widget.label,
          border: const OutlineInputBorder(),
          prefixIcon: widget.showLocationButton
              ? IconButton(
                  icon: const Icon(Icons.my_location, color: Colors.blue),
                  tooltip: 'Use current location',
                  onPressed: () {
                    if (widget.onUseCurrentLocation != null) {
                      widget.onUseCurrentLocation!();
                    }
                  },
                )
              : null,
          suffixIcon: widget.controller.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    widget.controller.clear();
                    setState(() {
                      _predictions = [];
                    });
                    _updateOverlay();
                  },
                )
              : null,
        ),
        readOnly: widget.readOnly,
      ),
    );
  }
}
