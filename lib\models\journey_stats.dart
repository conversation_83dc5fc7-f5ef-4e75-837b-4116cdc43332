import 'package:flutter/foundation.dart';

class JourneyStats {
  final String id;
  final DateTime completedAt;
  final String routeName;
  final String startAddress;
  final String endAddress;
  final String distance;
  final int distanceValue; // in meters
  final String duration;
  final int durationValue; // in seconds
  final double emissions; // in kg CO2
  final double fuelConsumption; // in litres
  final double fuelCost; // in pounds
  final String actualDuration; // formatted as HH:MM:SS
  final int actualDurationSeconds; // in seconds

  // Fastest route data for comparison
  final double fastestRouteEmissions; // emissions of the fastest route
  final double fastestRouteFuelConsumption; // fuel consumption of the fastest route
  final double fastestRouteFuelCost; // fuel cost of the fastest route

  // Savings compared to fastest route (positive = savings, negative = additional cost)
  final double emissionsSaved; // kg CO2 saved compared to fastest route
  final double fuelSaved; // litres saved compared to fastest route
  final double costSaved; // pounds saved compared to fastest route

  JourneyStats({
    required this.id,
    required this.completedAt,
    required this.routeName,
    required this.startAddress,
    required this.endAddress,
    required this.distance,
    required this.distanceValue,
    required this.duration,
    required this.durationValue,
    required this.emissions,
    required this.fuelConsumption,
    required this.fuelCost,
    required this.actualDuration,
    required this.actualDurationSeconds,
    this.fastestRouteEmissions = 0.0,
    this.fastestRouteFuelConsumption = 0.0,
    this.fastestRouteFuelCost = 0.0,
    this.emissionsSaved = 0.0,
    this.fuelSaved = 0.0,
    this.costSaved = 0.0,
  });

  // Create from JSON
  factory JourneyStats.fromJson(Map<String, dynamic> json) {
    return JourneyStats(
      id: json['id'] as String,
      completedAt: DateTime.parse(json['completedAt'] as String),
      routeName: json['routeName'] as String,
      startAddress: json['startAddress'] as String,
      endAddress: json['endAddress'] as String,
      distance: json['distance'] as String,
      distanceValue: json['distanceValue'] as int,
      duration: json['duration'] as String,
      durationValue: json['durationValue'] as int,
      emissions: json['emissions'] as double,
      fuelConsumption: json['fuelConsumption'] as double,
      fuelCost: json['fuelCost'] as double,
      actualDuration: json['actualDuration'] as String,
      actualDurationSeconds: json['actualDurationSeconds'] as int,
      fastestRouteEmissions: json['fastestRouteEmissions'] as double? ?? 0.0,
      fastestRouteFuelConsumption: json['fastestRouteFuelConsumption'] as double? ?? 0.0,
      fastestRouteFuelCost: json['fastestRouteFuelCost'] as double? ?? 0.0,
      emissionsSaved: json['emissionsSaved'] as double? ?? 0.0,
      fuelSaved: json['fuelSaved'] as double? ?? 0.0,
      costSaved: json['costSaved'] as double? ?? 0.0,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'completedAt': completedAt.toIso8601String(),
      'routeName': routeName,
      'startAddress': startAddress,
      'endAddress': endAddress,
      'distance': distance,
      'distanceValue': distanceValue,
      'duration': duration,
      'durationValue': durationValue,
      'emissions': emissions,
      'fuelConsumption': fuelConsumption,
      'fuelCost': fuelCost,
      'actualDuration': actualDuration,
      'actualDurationSeconds': actualDurationSeconds,
      'fastestRouteEmissions': fastestRouteEmissions,
      'fastestRouteFuelConsumption': fastestRouteFuelConsumption,
      'fastestRouteFuelCost': fastestRouteFuelCost,
      'emissionsSaved': emissionsSaved,
      'fuelSaved': fuelSaved,
      'costSaved': costSaved,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JourneyStats && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Class to hold aggregated journey statistics
class AggregatedJourneyStats {
  final int totalJourneys;
  final int totalDistanceMeters;
  final String totalDistance; // Formatted distance
  final int totalDurationSeconds;
  final String totalDuration; // Formatted duration
  final double totalEmissions;
  final double totalFuelConsumption;
  final double totalFuelCost;
  final int totalActualDurationSeconds;
  final String totalActualDuration; // Formatted actual duration

  // Savings totals
  final double totalEmissionsSaved;
  final double totalFuelSaved;
  final double totalCostSaved;

  AggregatedJourneyStats({
    required this.totalJourneys,
    required this.totalDistanceMeters,
    required this.totalDistance,
    required this.totalDurationSeconds,
    required this.totalDuration,
    required this.totalEmissions,
    required this.totalFuelConsumption,
    required this.totalFuelCost,
    required this.totalActualDurationSeconds,
    required this.totalActualDuration,
    this.totalEmissionsSaved = 0.0,
    this.totalFuelSaved = 0.0,
    this.totalCostSaved = 0.0,
  });

  // Helper method to format duration in seconds to HH:MM:SS
  static String formatDuration(int seconds) {
    final hours = (seconds / 3600).floor();
    final minutes = ((seconds % 3600) / 60).floor();
    final remainingSeconds = seconds % 60;

    final hoursStr = hours > 0 ? '${hours}h ' : '';
    final minutesStr = minutes > 0 ? '${minutes}m ' : '';
    final secondsStr = remainingSeconds > 0 ? '${remainingSeconds}s' : '';

    return '$hoursStr$minutesStr$secondsStr'.trim();
  }

  // Helper method to format distance in meters
  static String formatDistance(int meters) {
    if (meters < 1000) {
      return '$meters m';
    } else {
      final km = meters / 1000.0;
      return '${km.toStringAsFixed(1)} km';
    }
  }
}
